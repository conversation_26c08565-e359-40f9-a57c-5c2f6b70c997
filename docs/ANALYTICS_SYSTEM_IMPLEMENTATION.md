# Analytics System Implementation

## Overview

This document describes the comprehensive analytics system implemented for the bus pass booking project. The system provides real-time insights through both backend APIs and frontend components, replacing time-based analytics with current state data.

## Architecture

### Database Schema

#### New Tables

1. **analytics_revenue**
   - `id` (UUID, primary key)
   - `bus_route` (TEXT, references buses.route_code)
   - `bus_name` (TEXT)
   - `total_revenue` (DECIMAL(10,2))
   - `booking_count` (INTEGER)
   - `created_at` (TIMESTAMPTZ)
   - `updated_at` (TIMESTAMPTZ)

2. **booking_rounds**
   - `id` (UUID, primary key)
   - `go_date` (DATE)
   - `return_date` (DATE)
   - `total_bookings` (INTEGER)
   - `total_revenue` (DECIMAL(10,2))
   - `reset_date` (TIMESTAMPTZ)
   - `created_at` (TIMESTAMPTZ)

#### Database Functions

1. **update_analytics_revenue()** - Trigger function that updates analytics_revenue table when bookings change
2. **archive_booking_round()** - Archives current booking stats before reset
3. **reset_all_bookings()** - Updated to archive data before resetting

#### Triggers

- `update_analytics_revenue_trigger` - Fires on INSERT/UPDATE/DELETE of bookings table

### Backend API Endpoints

All endpoints use the established API response pattern: `{ success: boolean, data: object, error?: string }`

#### 1. Revenue Endpoint
- **URL**: `/api/admin/reports/revenue`
- **Method**: GET
- **Authentication**: Required (withAuth middleware)
- **Response**:
  ```typescript
  {
    success: true,
    data: {
      totalRevenue: number,
      routes: Array<{
        busRoute: string,
        busName: string,
        totalRevenue: number,
        bookingCount: number,
        revenuePerBooking: number
      }>
    }
  }
  ```

#### 2. Routes Endpoint
- **URL**: `/api/admin/reports/routes`
- **Method**: GET
- **Authentication**: Required
- **Response**:
  ```typescript
  {
    success: true,
    data: {
      routes: Array<{
        routeCode: string,
        busName: string,
        currentBookings: number,
        totalSeats: number,
        occupancyPercentage: number,
        isActive: boolean
      }>
    }
  }
  ```

#### 3. Stops Endpoint
- **URL**: `/api/admin/reports/stops?bus_route={route_code}`
- **Method**: GET
- **Authentication**: Required
- **Query Parameters**: `bus_route` (required)
- **Response**:
  ```typescript
  {
    success: true,
    data: {
      busRoute: string,
      busName: string,
      totalRouteBookings: number,
      stops: Array<{
        stopName: string,
        bookingCount: number,
        percentageOfRoute: number
      }>
    }
  }
  ```

#### 4. Rounds Endpoint
- **URL**: `/api/admin/reports/rounds`
- **Method**: GET
- **Authentication**: Required
- **Response**:
  ```typescript
  {
    success: true,
    data: {
      rounds: Array<{
        id: string,
        goDate: string,
        returnDate: string,
        totalBookings: number,
        totalRevenue: number,
        resetDate: string
      }>
    }
  }
  ```

### Frontend Implementation

#### Main Dashboard
- **Location**: `/app/admin/reports/page.tsx`
- **Authentication**: Protected with `withAdminAuth` HOC
- **Features**:
  - 4-tab interface (Revenue, Routes, Stops, History)
  - Auto-refresh every 5 minutes
  - Manual refresh button
  - Breadcrumb navigation
  - Loading states and error handling

#### Tab Components

1. **RevenueTab** (`/app/admin/reports/components/RevenueTab.tsx`)
   - Total revenue display card
   - Sortable revenue table
   - Performance badges (Top 25%, Bottom 25%, Average)
   - CSV export functionality
   - Currency formatting (INR)

2. **RoutesTab** (`/app/admin/reports/components/RoutesTab.tsx`)
   - Route occupancy cards with progress bars
   - Color-coded occupancy levels:
     - Green (0-60%): Available
     - Yellow (61-85%): Moderate Demand
     - Red (86-100%): High Demand
   - Summary statistics
   - Responsive grid layout

3. **StopsTab** (`/app/admin/reports/components/StopsTab.tsx`)
   - Route selector dropdown
   - Stop performance cards with rankings
   - Top 3 stops highlighted with icons
   - Percentage distribution visualization
   - Empty state handling

4. **HistoryTab** (`/app/admin/reports/components/HistoryTab.tsx`)
   - Historical booking rounds table
   - Sortable columns
   - Summary statistics
   - Date formatting
   - Pagination support (for >50 rounds)

### Navigation Integration

Updated admin dashboard navigation to include:
- **Label**: "Reports Dashboard"
- **Icon**: `BarChart3` from lucide-react
- **Route**: `/admin/reports`
- **Position**: After "Route Management"

### Reset Functionality Integration

The existing reset functionality has been enhanced to:
1. Archive current booking stats to `booking_rounds` table before resetting
2. Clear analytics_revenue table
3. Reset all booking counters and revenue in admin_settings
4. Maintain historical data for reporting

### TypeScript Interfaces

All data structures are properly typed in `/types/reports.ts`:
- API response interfaces
- Component prop interfaces
- Utility type definitions
- Form validation interfaces

### Performance Features

1. **Caching**: 5-minute cache headers on all API endpoints
2. **Loading States**: Skeleton components during data fetching
3. **Error Handling**: Comprehensive error boundaries with retry functionality
4. **Responsive Design**: Mobile-first approach with Tailwind CSS
5. **Accessibility**: ARIA labels and keyboard navigation support

### Security

1. **Authentication**: All endpoints protected with `withAuth` middleware
2. **RLS Policies**: Database-level security for analytics tables
3. **Input Validation**: Query parameter validation for stops endpoint
4. **Error Sanitization**: Safe error messages without sensitive data exposure

## Testing

### API Testing
- Test script: `/scripts/test-analytics-api.js`
- Tests all endpoints with various scenarios
- Validates response structures
- Tests error conditions

### Manual Testing Checklist
- [ ] All tabs load without errors
- [ ] Data refreshes correctly
- [ ] Sorting works on all tables
- [ ] Route selection updates stops data
- [ ] CSV export functions properly
- [ ] Mobile responsiveness
- [ ] Error states display correctly
- [ ] Reset functionality archives data

## Deployment Notes

1. **Database Migration**: Run `20250814000001_analytics_system.sql` migration
2. **Dependencies**: All required shadcn/ui components are already installed
3. **Environment**: No additional environment variables required
4. **Permissions**: Ensure admin users have access to new endpoints

## Future Enhancements

1. **Data Visualization**: Add charts and graphs using Chart.js or similar
2. **Export Options**: PDF reports, Excel exports
3. **Scheduled Reports**: Email reports to administrators
4. **Advanced Filtering**: Date range filters, route-specific analytics
5. **Real-time Updates**: WebSocket integration for live data updates

## Maintenance

1. **Database Cleanup**: Consider archiving old booking_rounds data periodically
2. **Performance Monitoring**: Monitor query performance on analytics tables
3. **Cache Optimization**: Adjust cache durations based on usage patterns
4. **User Feedback**: Collect admin feedback for UI/UX improvements
