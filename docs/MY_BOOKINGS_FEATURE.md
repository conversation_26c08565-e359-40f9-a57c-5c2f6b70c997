# My Bookings Feature

## Overview

The "My Bookings" feature allows students to search and view their bus pass bookings using their admission number. This feature provides a secure and user-friendly way to check booking status and details.

## Features

### 1. Landing Page Enhancement
- Added "My Bookings" button on the landing page
- Positioned beneath the existing portal section
- Consistent styling with existing UI components
- Smooth animations and transitions

### 2. My Bookings Page (`/my-bookings`)
- Clean, responsive design with mobile-first approach
- Search form with admission number input
- Real-time validation and error handling
- Loading states and user feedback
- Results displayed in both card (mobile) and table (desktop) formats

### 3. API Endpoint (`/api/bookings/search`)
- RESTful GET endpoint for searching bookings
- Comprehensive input validation
- Rate limiting protection
- SQL injection prevention
- Proper error handling and responses

### 4. Security Features
- Admission number format validation (XXAA000 pattern)
- Input sanitization and normalization
- Rate limiting (10 requests per minute per IP)
- SQL injection prevention through parameterized queries
- Proper error messages without exposing system details

## Technical Implementation

### Frontend Components

#### Landing Page Updates (`app/page.tsx`)
```typescript
// Added My Bookings section with:
- Motion animations for smooth transitions
- Consistent card styling
- Responsive design
- Navigation to /my-bookings route
```

#### My Bookings Page (`app/my-bookings/page.tsx`)
```typescript
// Key features:
- Form validation with real-time feedback
- Loading states and error handling
- Responsive design (cards for mobile, table for desktop)
- Date formatting and status badges
- Toast notifications for user feedback
```

### Backend API

#### Search Endpoint (`app/api/bookings/search/route.ts`)
```typescript
// Security measures:
- Rate limiting (10 requests/minute per IP)
- Input validation with regex pattern
- Input sanitization (uppercase conversion)
- Parameterized SQL queries
- Comprehensive error handling
```

### Database Schema

The feature works with the existing `bookings` table structure:

```sql
CREATE TABLE bookings (
  id SERIAL PRIMARY KEY,
  admission_number VARCHAR(7) NOT NULL,
  student_name VARCHAR(100) NOT NULL,
  bus_route VARCHAR(50) NOT NULL,
  destination VARCHAR(100) NOT NULL,
  go_date DATE,
  return_date DATE,
  fare DECIMAL(10,2),
  payment_status BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  -- Additional Razorpay fields...
);
```

## Usage

### For Students
1. Visit the landing page
2. Click "My Bookings" button
3. Enter admission number in format: XXAA000
4. Click "Search Bookings"
5. View booking details and status

### For Developers
1. Start the development server: `npm run dev`
2. Visit `http://localhost:3000`
3. Test the feature using the test script: `node scripts/test-my-bookings.js`

## API Documentation

### GET /api/bookings/search

**Query Parameters:**
- `admission_number` (required): Student admission number in XXAA000 format

**Response Format:**
```json
{
  "success": true,
  "data": {
    "bookings": [
      {
        "id": 1,
        "admission_number": "22CS001",
        "student_name": "John Doe",
        "bus_route": "route-1",
        "destination": "Kottayam",
        "go_date": "2024-01-15",
        "return_date": "2024-01-20",
        "fare": 150.00,
        "payment_status": true,
        "created_at": "2024-01-10T10:30:00Z"
      }
    ],
    "count": 1
  }
}
```

**Error Responses:**
- `400`: Invalid admission number format
- `429`: Rate limit exceeded
- `500`: Server error

## Security Considerations

### Input Validation
- Admission number must match pattern: `^\d{2}[A-Za-z]{2}\d{3}$`
- Maximum length enforcement
- Case normalization (uppercase)

### Rate Limiting
- 10 requests per minute per IP address
- In-memory storage (consider Redis for production)
- Automatic reset after window expires

### SQL Injection Prevention
- Parameterized queries using Supabase client
- Input sanitization before database queries
- No direct SQL string concatenation

### Error Handling
- Generic error messages for security
- Detailed logging for debugging
- Proper HTTP status codes

## Testing

### Manual Testing
1. Test with valid admission numbers
2. Test with invalid formats
3. Test rate limiting by making multiple requests
4. Test responsive design on different screen sizes

### Automated Testing
Run the test script:
```bash
node scripts/test-my-bookings.js
```

## Future Enhancements

1. **Authentication**: Add student login system
2. **Caching**: Implement Redis for rate limiting and caching
3. **Notifications**: Add email/SMS notifications for booking updates
4. **Export**: Allow booking details export (PDF/CSV)
5. **Analytics**: Track search patterns and popular routes

## Dependencies

- Next.js 14+ for frontend and API routes
- Supabase for database operations
- Framer Motion for animations
- Lucide React for icons
- Tailwind CSS for styling
- Sonner for toast notifications

## File Structure

```
app/
├── api/
│   └── bookings/
│       └── search/
│           └── route.ts          # API endpoint
├── my-bookings/
│   └── page.tsx                  # My Bookings page
└── page.tsx                      # Updated landing page

scripts/
└── test-my-bookings.js           # Test script

docs/
└── MY_BOOKINGS_FEATURE.md        # This documentation
```

## Deployment Notes

1. Ensure environment variables are set for Supabase
2. Consider implementing Redis for production rate limiting
3. Set up proper monitoring and logging
4. Test thoroughly in staging environment
5. Update documentation for production deployment 