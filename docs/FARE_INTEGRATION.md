# Fare Integration in Bookings

## Overview

This enhancement adds a `fare` column to the `bookings` table to store the fare amount for the selected destination. The fare value is automatically retrieved from the `route_stops` table based on the booking's route and destination combination, ensuring accurate fare tracking and reporting.

## Database Changes

### Migration: `20250113000000_add_fare_to_bookings.sql`

- **Added column:**
  - `fare` (DECIMAL(10,2), CHECK fare >= 0) - The fare amount for the selected destination, retrieved from route_stops table

- **Added indexes:**
  - `idx_bookings_fare` - For efficient queries on fare field
  - `idx_bookings_route_destination_fare` - Composite index for fare-related queries

## API Changes

### Booking Creation (`/api/bookings`)

**Before:**
```typescript
// Booking creation without fare
const bookingData = {
  admission_number: admissionNumber,
  student_name: studentName,
  bus_route: busRoute,
  destination: destination,
  payment_status: paymentStatus,
  // ... other fields
};
```

**After:**
```typescript
// Fetch fare from route_stops table based on route and destination
const { data: routeStop, error: fareError } = await supabaseAdmin
  .from('route_stops')
  .select('fare')
  .eq('route_code', busRoute)
  .eq('stop_name', destination)
  .eq('is_active', true)
  .single();

if (fareError || !routeStop) {
  return NextResponse.json({ 
    error: 'Failed to fetch fare information',
    details: `No fare found for route ${busRoute} and destination ${destination}`
  }, { status: 400 });
}

// Include fare in booking creation
const bookingData = {
  admission_number: admissionNumber,
  student_name: studentName,
  bus_route: busRoute,
  destination: destination,
  payment_status: paymentStatus,
  fare: routeStop.fare,  // NEW
  // ... other fields
};
```

### Booking Statistics (`/api/admin/bookings/stats`)

**Before:**
```typescript
// Estimated revenue calculation
const estimatedRevenue = (paidBookings || 0) * 50; // Average fare estimate
```

**After:**
```typescript
// Get total revenue from actual fare data
const { data: paidBookingsWithFare } = await supabaseAdmin
  .from('bookings')
  .select('fare')
  .eq('payment_status', true)
  .not('fare', 'is', null);

// Calculate actual revenue from stored fare data
const actualRevenue = paidBookingsWithFare?.reduce((total, booking) => {
  return total + (booking.fare || 0);
}, 0) || 0;

// Fallback to estimated revenue if no fare data is available
const estimatedRevenue = (paidBookings || 0) * 50;
const totalRevenue = actualRevenue > 0 ? actualRevenue : estimatedRevenue;
```

## TypeScript Interface Updates

### Booking Interface

```typescript
export interface Booking {
  studentName: string;
  admissionNumber: string;
  busRoute: string;
  destination: string;
  paymentStatus: boolean;
  timestamp: string;
  goDate?: string | null;
  returnDate?: string | null;
  fare?: number | null;  // NEW
}
```

## Benefits

1. **Accurate Fare Tracking**: Each booking now stores the exact fare for the selected destination
2. **Revenue Reporting**: Enables precise revenue calculations based on actual fares
3. **Data Integrity**: Fare values are validated against the route_stops table
4. **Historical Accuracy**: Fare changes in route_stops won't affect existing bookings
5. **Audit Trail**: Complete record of what fare was charged for each booking

## Usage Examples

### Querying Bookings by Fare

```sql
-- Find all bookings with fare above a certain amount
SELECT * FROM bookings WHERE fare > 100;

-- Find average fare by route
SELECT bus_route, AVG(fare) as avg_fare 
FROM bookings 
GROUP BY bus_route 
ORDER BY avg_fare DESC;

-- Find total revenue by destination
SELECT destination, SUM(fare) as total_revenue 
FROM bookings 
WHERE payment_status = true 
GROUP BY destination;
```

### Admin Dashboard Queries

```sql
-- Revenue by month
SELECT 
  DATE_TRUNC('month', created_at) as month,
  SUM(fare) as total_revenue,
  COUNT(*) as booking_count
FROM bookings 
WHERE payment_status = true 
GROUP BY month 
ORDER BY month;

-- Top destinations by revenue
SELECT 
  destination,
  SUM(fare) as total_revenue,
  COUNT(*) as booking_count
FROM bookings 
WHERE payment_status = true 
GROUP BY destination 
ORDER BY total_revenue DESC 
LIMIT 10;
```

## Error Handling

The booking creation API now includes comprehensive error handling for fare retrieval:

1. **Invalid Route/Destination**: Returns 400 error if no fare is found for the route-destination combination
2. **Inactive Route Stops**: Only considers active route stops when fetching fares
3. **Missing Fare Data**: Provides clear error messages for debugging

## Testing

Use the provided test script to verify the implementation:

```bash
node scripts/test-fare-integration.js
```

This script will:
1. Check that route_stops table has fare data
2. Create a test booking with fare retrieval
3. Verify the booking includes the correct fare
4. Test the booking statistics API with actual fare data
5. Clean up test data

## Migration Notes

- The migration adds a nullable column, so existing bookings will have `NULL` values for `fare`
- New bookings created after the migration will automatically include the fare from route_stops
- The booking statistics API will use actual fare data when available, falling back to estimates for older bookings

## Security Considerations

- Fare data is fetched using the admin client to ensure proper access control
- The booking API validates that the route-destination combination exists before creating the booking
- All existing RLS policies remain in place

## Future Enhancements

Potential future improvements:
1. Add fare validation rules (e.g., minimum/maximum fare limits)
2. Implement fare discounts or promotions
3. Add fare history tracking for route changes
4. Create fare comparison reports across different routes
5. Add fare-based booking analytics and insights 