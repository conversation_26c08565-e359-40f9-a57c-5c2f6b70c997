# Reset Functionality Fix - 500 Error Resolution

## Problem Diagnosis

The reset functionality is failing with a 500 error when making a POST request to `/api/admin/analytics/reset`. The root cause has been identified as conflicting database function definitions.

### Root Cause Analysis

1. **Multiple Function Definitions**: The `reset_all_bookings()` function is defined in multiple migration files:
   - `20250814000001_analytics_system.sql` (August 14) - Includes archiving functionality
   - `20250116000000_add_current_revenue_to_admin_settings.sql` (January 16) - Overwrites without archiving

2. **Migration Order Issue**: Despite the August migration being newer, the January migration runs AFTER it due to date ordering (2025-01-16 > 2025-08-14), causing the January version to override the correct August version.

3. **Missing Archive Call**: The January version doesn't call `archive_booking_round()`, breaking the complete reset workflow.

## Solution Implemented

### 1. Updated January Migration File

Fixed `supabase/migrations/20250116000000_add_current_revenue_to_admin_settings.sql` to include:

- **Archive Call**: Added safe call to `archive_booking_round()` with error handling
- **Analytics Reset**: Added deletion of `analytics_revenue` table with error handling
- **Permissions**: Added proper EXECUTE permissions for the function

### 2. Updated August Migration File

Enhanced `supabase/migrations/20250814000001_analytics_system.sql` to:

- **Correct Total Bookings**: Fixed `archive_booking_round()` to use `paid_bookings + unpaid_bookings`
- **Better Logging**: Added detailed logging for archival process
- **Permissions**: Added EXECUTE permission for `reset_all_bookings()`

### 3. Error Handling

Both functions now include comprehensive error handling:

```sql
BEGIN
  PERFORM archive_booking_round();
EXCEPTION
  WHEN undefined_function THEN
    RAISE NOTICE 'archive_booking_round() function not found, skipping archival';
  WHEN OTHERS THEN
    RAISE NOTICE 'Error archiving booking round: %', SQLERRM;
END;
```

## Complete Reset Workflow

The fixed reset functionality now performs these operations in order:

### 1. Archive Current Data
- Retrieves current `paid_bookings`, `unpaid_bookings`, and `current_revenue` from `admin_settings`
- Calculates total bookings as `paid_bookings + unpaid_bookings`
- Inserts record into `booking_rounds` table with:
  - `go_date` and `return_date` from admin_settings
  - `total_bookings` (paid + unpaid)
  - `total_revenue` (current revenue amount)
  - `reset_date` (current timestamp)

### 2. Reset Admin Settings
Updates `admin_settings` table:
```sql
UPDATE admin_settings
SET current_bookings = 0,
    paid_bookings = 0,
    unpaid_bookings = 0,
    current_revenue = 0.00,
    updated_at = NOW()
WHERE id = 1;
```

### 3. Reset Bus Seats
Updates all active buses:
```sql
UPDATE buses
SET available_seats = total_seats,
    updated_at = NOW()
WHERE is_active = true;
```

### 4. Clear Analytics Data
Clears the analytics revenue table:
```sql
DELETE FROM analytics_revenue;
```

## Files Modified

1. **`supabase/migrations/20250116000000_add_current_revenue_to_admin_settings.sql`**
   - Updated `reset_all_bookings()` function to include archiving
   - Added error handling for missing functions/tables
   - Added proper permissions

2. **`supabase/migrations/20250814000001_analytics_system.sql`**
   - Fixed `archive_booking_round()` to use total bookings (paid + unpaid)
   - Enhanced logging and error messages
   - Added missing permissions

## Testing and Verification

### Manual Database Fix

If the migrations haven't been re-run, execute the SQL script:
```bash
# Run this in your Supabase SQL editor
scripts/fix-reset-functions.sql
```

### API Testing

Test the reset functionality:
```bash
# Run the test script
node scripts/test-reset-functionality.js
```

### Verification Steps

1. **Test Reset API**: POST to `/api/admin/analytics/reset` should return 200 with success
2. **Check Archival**: GET `/api/admin/reports/rounds` should show archived data
3. **Verify Reset**: GET `/api/admin/analytics/detailed-stats` should show all zeros
4. **Check History Tab**: Analytics dashboard History tab should display archived rounds

## Expected API Responses

### Successful Reset Response
```json
{
  "success": true,
  "data": {
    "message": "All booking data reset successfully using database functions",
    "deletedBookings": true,
    "resetSeats": true,
    "resetStatistics": true
  },
  "error": null
}
```

### Booking Rounds Response (After Reset)
```json
{
  "success": true,
  "data": {
    "rounds": [
      {
        "id": "uuid",
        "goDate": "2025-01-15",
        "returnDate": "2025-01-16",
        "totalBookings": 25,
        "totalRevenue": 1250.00,
        "resetDate": "2025-01-16T10:30:00Z"
      }
    ]
  }
}
```

## Prevention Measures

1. **Migration Naming**: Use proper date ordering for migrations
2. **Function Versioning**: Include version numbers in function names if needed
3. **Comprehensive Testing**: Test all database functions after migrations
4. **Documentation**: Document all function dependencies and interactions

## Rollback Plan

If issues persist, the original function can be restored by running:

```sql
-- Restore basic reset function without archiving
CREATE OR REPLACE FUNCTION reset_all_bookings()
RETURNS VOID AS $$
BEGIN
  UPDATE buses SET available_seats = total_seats WHERE is_active = true;
  UPDATE admin_settings SET current_bookings = 0, paid_bookings = 0, 
         unpaid_bookings = 0, current_revenue = 0.00 WHERE id = 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Next Steps

1. **Apply Database Fix**: Run the fix script in Supabase SQL editor
2. **Test Reset**: Verify the reset functionality works end-to-end
3. **Monitor Logs**: Check for any remaining errors in the application logs
4. **Update Documentation**: Ensure all team members are aware of the fix
