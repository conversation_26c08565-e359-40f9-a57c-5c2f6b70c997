# Reset Functionality - Complete Fix for 500 Error

## Problem Summary

The reset functionality was failing with a 500 error when calling `POST /api/admin/analytics/reset` due to conflicting database function definitions across multiple migration files.

## Root Cause Analysis

1. **Multiple Function Definitions**: The `reset_all_bookings()` function was defined in several migration files:
   - `20250110000000_booking_management_functions.sql` - Basic version
   - `20250814000001_analytics_system.sql` - Version with archiving
   - `20250116000000_add_current_revenue_to_admin_settings.sql` - Updated version

2. **Migration Order Conflict**: Due to date ordering, the January 16 migration ran after the August 14 migration, potentially overwriting the correct function.

3. **Schema Variations**: Different migrations assumed different table structures (buses vs bus_availability tables).

## Complete Solution

### 1. New Migration File: `20250116120000_fix_reset_functionality.sql`

This migration provides a definitive fix that:

- ✅ **Creates the correct `reset_all_bookings()` function** with complete archiving workflow
- ✅ **Handles schema variations** (supports both buses and bus_availability tables)
- ✅ **Includes comprehensive error handling** to prevent 500 errors
- ✅ **Is idempotent** - safe to run multiple times
- ✅ **Includes verification tests** to ensure the function works

### 2. Function Features

#### Archive Workflow (`archive_booking_round()`)
```sql
-- Calculates total_bookings as paid_bookings + unpaid_bookings
-- Archives to booking_rounds table with go_date, return_date, total_revenue
-- Only archives if there's actual data to preserve
-- Includes error handling to prevent blocking reset operation
```

#### Reset Workflow (`reset_all_bookings()`)
```sql
-- 1. Archive current data (with error handling)
-- 2. Reset bus seats (handles both table structures)
-- 3. Reset admin_settings (all counters and revenue to 0)
-- 4. Clear analytics_revenue table (if exists)
-- 5. Comprehensive logging for debugging
```

### 3. Error Handling Strategy

The new implementation uses a **graceful degradation** approach:

- **Archive errors**: Logged as warnings, don't block reset
- **Bus seat errors**: Tries multiple table structures, logs warnings
- **Analytics errors**: Handles missing tables gracefully
- **Critical errors**: Only thrown for fundamental failures

## Implementation Steps

### Step 1: Apply the Migration

Run the migration in your Supabase SQL editor:

```sql
-- Copy and paste the contents of:
-- supabase/migrations/20250116120000_fix_reset_functionality.sql
```

**OR** if using Supabase CLI:

```bash
supabase db push
```

### Step 2: Verify the Fix

Run the verification script:

```bash
node scripts/verify-reset-fix.js
```

Expected output:
```
🎉 ALL TESTS PASSED!
✅ Reset functionality is working correctly
✅ The 500 error has been resolved
```

### Step 3: Test End-to-End

1. **Login to Admin Dashboard**
2. **Navigate to Admin Panel**
3. **Click "Reset All Bookings" button**
4. **Verify Success Response**
5. **Check History Tab** in Reports Dashboard for archived data

## Expected Behavior After Fix

### 1. Reset API Response (200 OK)
```json
{
  "success": true,
  "data": {
    "message": "All booking data reset successfully using database functions",
    "deletedBookings": true,
    "resetSeats": true,
    "resetStatistics": true
  }
}
```

### 2. Database Changes
- **admin_settings**: All counters and revenue reset to 0
- **buses/bus_availability**: Available seats reset to total capacity
- **booking_rounds**: New record with archived data (if any existed)
- **analytics_revenue**: Cleared of all records

### 3. Reports Dashboard
- **History Tab**: Shows archived booking rounds
- **Other Tabs**: Show fresh data starting from zero

## Troubleshooting

### If 500 Error Persists

1. **Check Migration Applied**:
   ```sql
   SELECT * FROM _supabase_migrations 
   WHERE version = '20250116120000';
   ```

2. **Verify Function Exists**:
   ```sql
   SELECT routine_name, routine_type 
   FROM information_schema.routines 
   WHERE routine_name = 'reset_all_bookings';
   ```

3. **Test Function Manually**:
   ```sql
   SELECT reset_all_bookings();
   ```

### If Authentication Issues

- Ensure you're logged in as an admin user
- Check that the `withAuth` middleware is working
- Verify admin session is valid

### If Data Not Archiving

- Check that `booking_rounds` table exists
- Verify `admin_settings` has required columns
- Look for function logs in Supabase logs panel

## Database Function Logs

The functions now include comprehensive logging. Check your Supabase logs for messages like:

```
NOTICE: Archived booking round with 25 total bookings (20 paid, 5 unpaid) and 1250.00 revenue
NOTICE: Reset available_seats in buses table
NOTICE: Cleared analytics_revenue table
NOTICE: All booking statistics, bus seats, revenue, and analytics have been reset successfully.
```

## Rollback Plan

If issues occur, you can rollback to a basic reset function:

```sql
CREATE OR REPLACE FUNCTION reset_all_bookings()
RETURNS VOID AS $$
BEGIN
  UPDATE admin_settings 
  SET current_bookings = 0, paid_bookings = 0, 
      unpaid_bookings = 0, current_revenue = 0.00 
  WHERE id = 1;
  
  UPDATE buses 
  SET available_seats = COALESCE(total_seats, 50) 
  WHERE is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Prevention Measures

1. **Consistent Migration Naming**: Use proper timestamps
2. **Function Versioning**: Consider version numbers in function names
3. **Comprehensive Testing**: Test all database functions after changes
4. **Documentation**: Document all function dependencies

## Success Criteria

✅ **POST /api/admin/analytics/reset returns 200 OK**  
✅ **Data is properly archived before reset**  
✅ **All statistics reset to zero**  
✅ **Bus seats reset to full capacity**  
✅ **History tab shows archived data**  
✅ **No 500 errors in any scenario**  

The reset functionality should now work reliably with complete data archiving and proper error handling.
