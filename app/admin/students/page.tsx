'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { PageTransition } from '@/components/ui/page-transition';
import { withAdminAuth } from '@/contexts/AdminContext';
import { Users, Plus, Edit, Trash2, Save, X, ArrowLeft, Search, ChevronUp, ChevronDown, ChevronLeft, ChevronRight } from 'lucide-react';
import { motion } from 'framer-motion';
import { toast } from 'sonner';
import Link from 'next/link';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface Student {
  id: string;
  name: string;
  admission_number: string;
  hostel: string | null;
  route: string | null;
  created_at: string;
  updated_at: string;
}

interface StudentFormData {
  name: string;
  admission_number: string;
  hostel: string;
  route: string;
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}
type SortField = 'name' | 'admission_number' | 'hostel' | 'route' | 'created_at';
type SortOrder = 'asc' | 'desc';

const hostelOptions = ['St Mary\'s Hostel', 'St Alphonsa Hostel','St Augustine Hostel','St Thomas Hostel'];
const routeOptions = ['Cheenikuzhy', 'Muvatupuzha', 'Teekoy', 'Aruvithura'];

function StudentManagement() {
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<SortField>('created_at');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0
  });
  
  // Form states
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingStudent, setEditingStudent] = useState<Student | null>(null);
  const [formData, setFormData] = useState<StudentFormData>({
    name: '',
    admission_number: '',
    hostel: '',
    route: ''
  });
  const [formLoading, setFormLoading] = useState(false);

  // Fetch students
  const fetchStudents = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        sortBy: sortField,
        sortOrder: sortOrder,
        ...(searchTerm && { search: searchTerm })
      });

      const response = await fetch(`/api/admin/students?${params}`, {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch students');
      }

      const result = await response.json();
      if (result.success) {
        setStudents(result.data.students);
        setPagination(result.data.pagination);
      } else {
        throw new Error(result.error || 'Failed to fetch students');
      }
    } catch (error) {
      console.error('Error fetching students:', error);
      toast.error('Failed to fetch students');
    } finally {
      setLoading(false);
    }
  };

  // Load students on component mount and when dependencies change
  useEffect(() => {
    fetchStudents();
  }, [pagination.page, sortField, sortOrder, searchTerm]);

  // Handle search with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (pagination.page !== 1) {
        setPagination(prev => ({ ...prev, page: 1 }));
      } else {
        fetchStudents();
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      admission_number: '',
      hostel: '',
      route: ''
    });
    setEditingStudent(null);
    setShowAddForm(false);
  };

  // Handle add student
  const handleAddStudent = () => {
    resetForm();
    setShowAddForm(true);
  };

  // Handle edit student
  const handleEditStudent = (student: Student) => {
    setFormData({
      name: student.name,
      admission_number: student.admission_number,
      hostel: student.hostel || '',
      route: student.route || ''
    });
    setEditingStudent(student);
    setShowAddForm(true);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.admission_number.trim()) {
      toast.error('Name and admission number are required');
      return;
    }

    setFormLoading(true);

    try {
      const url = '/api/admin/students';
      const method = editingStudent ? 'PUT' : 'POST';
      const body = editingStudent 
        ? { ...formData, id: editingStudent.id }
        : formData;

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
        credentials: 'include'
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success(editingStudent ? 'Student updated successfully' : 'Student added successfully');
        resetForm();
        fetchStudents();
      } else {
        throw new Error(result.error || 'Operation failed');
      }
    } catch (error) {
      console.error('Error saving student:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to save student');
    } finally {
      setFormLoading(false);
    }
  };

  // Handle delete student
  const handleDeleteStudent = async (student: Student) => {
    if (!confirm(`Are you sure you want to delete ${student.name}? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/students?id=${student.id}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Student deleted successfully');
        fetchStudents();
      } else {
        throw new Error(result.error || 'Failed to delete student');
      }
    } catch (error) {
      console.error('Error deleting student:', error);
      toast.error('Failed to delete student');
    }
  };

  // Render sort icon
  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ChevronUp className="w-4 h-4 opacity-30" />;
    }
    return sortOrder === 'asc' ? 
      <ChevronUp className="w-4 h-4" /> : 
      <ChevronDown className="w-4 h-4" />;
  };

  return (
    <PageTransition>
      <div className="min-h-screen p-4 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="flex justify-between items-center mb-8"
          >
            <div className="flex items-center gap-4">
              <Link href="/admin/dashboard">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <div>
                <h1 className="text-3xl font-bold text-gray-800 mb-2">Student Management</h1>
                <p className="text-gray-600">Manage student information and records</p>
              </div>
            </div>
            <Button
              onClick={handleAddStudent}
              className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Student
            </Button>
          </motion.div>

          {/* Search and Filters */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-6"
          >
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-4">
                  <div className="relative flex-1 max-w-md">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Search students..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <div className="text-sm text-gray-600">
                    {pagination.total} student{pagination.total !== 1 ? 's' : ''} found
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Add/Edit Form */}
          {showAddForm && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-6"
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    {editingStudent ? 'Edit Student' : 'Add New Student'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">Name *</Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                          placeholder="Enter student name"
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="admission_number">Admission Number *</Label>
                        <Input
                          id="admission_number"
                          value={formData.admission_number}
                          onChange={(e) => {
                            // Convert to uppercase
                            const value = e.target.value.toUpperCase();
                            // Allow typing by only validating when the field is complete
                            // This accepts input during typing but ensures final format is correct
                            if (value.length <= 9) {
                              setFormData(prev => ({ ...prev, admission_number: value }));
                              
                              // Validate completed input against required format
                              if (value.length === 9) {
                                const regex = /^\d{2}[A-Za-z]{2,4}\d{3}$/;
                                if (!regex.test(value)) {
                                  toast.error('Invalid admission number format. Use 00XX000, 00XXX000, or 00XXXX000 format.');
                                }
                              }
                            }
                          }}
                          placeholder="Enter admission number (e.g., 00XX000)"
                          maxLength={9}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="hostel">Hostel</Label>
                        <Select
                          value={formData.hostel}
                          onValueChange={(value) => {
                            setFormData(prev => ({ ...prev, hostel: value }));
                          }}
                        >
                          <SelectTrigger id="hostel">
                            <SelectValue placeholder="Select a hostel" />
                          </SelectTrigger>
                          <SelectContent>
                            {hostelOptions.map((hostel) => (
                              <SelectItem key={hostel} value={hostel}>
                                {hostel}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="route">Route</Label>
                        <Select
                          value={formData.route}
                          onValueChange={(value) => {
                            setFormData(prev => ({ ...prev, route: value }));
                          }}
                        >
                          <SelectTrigger id="route">
                            <SelectValue placeholder="Select a route" />
                          </SelectTrigger>
                          <SelectContent>
                            {routeOptions.map((route) => (
                              <SelectItem key={route} value={route}>
                                {route}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div className="flex gap-2 pt-4">
                      <Button
                        type="submit"
                        disabled={formLoading}
                        className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700"
                      >
                        <Save className="w-4 h-4 mr-2" />
                        {formLoading ? 'Saving...' : (editingStudent ? 'Update Student' : 'Add Student')}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={resetForm}
                        disabled={formLoading}
                      >
                        <X className="w-4 h-4 mr-2" />
                        Cancel
                      </Button>
                    </div>
                  </form>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Students Table */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Students List
                </CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
                    <span className="ml-2">Loading students...</span>
                  </div>
                ) : students.length === 0 ? (
                  <div className="text-center py-12">
                    <Users className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-semibold text-gray-600 mb-2">No Students Found</h3>
                    <p className="text-gray-500 mb-4">
                      {searchTerm ? 'No students match your search criteria.' : 'Get started by adding your first student.'}
                    </p>
                    {!searchTerm && (
                      <Button
                        onClick={handleAddStudent}
                        className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Add First Student
                      </Button>
                    )}
                  </div>
                ) : (
                  <>
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead
                              className="cursor-pointer hover:bg-gray-50 select-none"
                              onClick={() => handleSort('name')}
                            >
                              <div className="flex items-center gap-1">
                                Name
                                {renderSortIcon('name')}
                              </div>
                            </TableHead>
                            <TableHead
                              className="cursor-pointer hover:bg-gray-50 select-none"
                              onClick={() => handleSort('admission_number')}
                            >
                              <div className="flex items-center gap-1">
                                Admission Number
                                {renderSortIcon('admission_number')}
                              </div>
                            </TableHead>
                            <TableHead
                              className="cursor-pointer hover:bg-gray-50 select-none"
                              onClick={() => handleSort('hostel')}
                            >
                              <div className="flex items-center gap-1">
                                Hostel
                                {renderSortIcon('hostel')}
                              </div>
                            </TableHead>
                            <TableHead
                              className="cursor-pointer hover:bg-gray-50 select-none"
                              onClick={() => handleSort('route')}
                            >
                              <div className="flex items-center gap-1">
                                Route
                                {renderSortIcon('route')}
                              </div>
                            </TableHead>
                            <TableHead
                              className="cursor-pointer hover:bg-gray-50 select-none"
                              onClick={() => handleSort('created_at')}
                            >
                              <div className="flex items-center gap-1">
                                Created
                                {renderSortIcon('created_at')}
                              </div>
                            </TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {students.map((student) => (
                            <TableRow key={student.id}>
                              <TableCell className="font-medium">{student.name}</TableCell>
                              <TableCell>
                                <Badge variant="outline" className="font-mono">
                                  {student.admission_number}
                                </Badge>
                              </TableCell>
                              <TableCell>{student.hostel || '-'}</TableCell>
                              <TableCell>
                                {student.route ? (
                                  <Badge variant="secondary">{student.route}</Badge>
                                ) : (
                                  '-'
                                )}
                              </TableCell>
                              <TableCell>
                                {new Date(student.created_at).toLocaleDateString()}
                              </TableCell>
                              <TableCell>
                                <div className="flex gap-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handleEditStudent(student)}
                                  >
                                    <Edit className="w-4 h-4" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-red-600 border-red-600 hover:bg-red-50"
                                    onClick={() => handleDeleteStudent(student)}
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>

                    {/* Pagination */}
                    {pagination.totalPages > 1 && (
                      <div className="flex items-center justify-between mt-6">
                        <div className="text-sm text-gray-600">
                          Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                          {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                          {pagination.total} students
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(pagination.page - 1)}
                            disabled={pagination.page === 1}
                          >
                            <ChevronLeft className="w-4 h-4" />
                            Previous
                          </Button>
                          <div className="flex items-center gap-1">
                            {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                              const pageNum = i + 1;
                              return (
                                <Button
                                  key={pageNum}
                                  variant={pagination.page === pageNum ? "default" : "outline"}
                                  size="sm"
                                  onClick={() => handlePageChange(pageNum)}
                                  className="w-8 h-8 p-0"
                                >
                                  {pageNum}
                                </Button>
                              );
                            })}
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(pagination.page + 1)}
                            disabled={pagination.page === pagination.totalPages}
                          >
                            Next
                            <ChevronRight className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </PageTransition>
  );
}

export default withAdminAuth(StudentManagement);
