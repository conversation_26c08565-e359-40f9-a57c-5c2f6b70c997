import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdmin } from '@/lib/supabase-admin';
import { withAuth, createApiResponse, handleApiError, validateRequestBody } from '@/lib/middleware';

// GET /api/admin/students - Get all students with pagination and sorting
export async function GET(request: NextRequest) {
  return withAuth(request, async (req) => {
    try {
      const { searchParams } = new URL(req.url);
      const page = parseInt(searchParams.get('page') || '1');
      const limit = parseInt(searchParams.get('limit') || '50');
      const sortBy = searchParams.get('sortBy') || 'created_at';
      const sortOrder = searchParams.get('sortOrder') || 'desc';
      const search = searchParams.get('search') || '';

      const supabaseAdmin = createSupabaseAdmin();
      
      // Build query
      let query = supabaseAdmin
        .from('students')
        .select('*', { count: 'exact' });

      // Add search filter if provided
      if (search) {
        query = query.or(`name.ilike.%${search}%,admission_number.ilike.%${search}%,hostel.ilike.%${search}%,route.ilike.%${search}%`);
      }

      // Add sorting
      const ascending = sortOrder === 'asc';
      query = query.order(sortBy, { ascending });

      // Add pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to);

      const { data: students, error, count } = await query;

      if (error) {
        throw error;
      }

      return createApiResponse({
        students: students || [],
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      });
    } catch (error) {
      return handleApiError(error, 'Failed to fetch students');
    }
  });
}

// POST /api/admin/students - Create a new student
export async function POST(request: NextRequest) {
  return withAuth(request, async (req) => {
    try {
      const body = await req.json();
      
      // Validate request body
      const validation = validateRequestBody<{
        name: string;
        admission_number: string;
        hostel?: string;
        route?: string;
      }>(body, ['name', 'admission_number']);

      if (!validation.isValid) {
        return NextResponse.json(
          { error: 'Validation failed', details: validation.errors },
          { status: 400 }
        );
      }

      const { name, admission_number, hostel, route } = body;
      const supabaseAdmin = createSupabaseAdmin();

      // Check if admission number already exists
      const { data: existingStudent } = await supabaseAdmin
        .from('students')
        .select('id')
        .eq('admission_number', admission_number)
        .single();

      if (existingStudent) {
        return NextResponse.json(
          { error: 'Student with this admission number already exists' },
          { status: 409 }
        );
      }

      // Create new student
      const { data: newStudent, error } = await supabaseAdmin
        .from('students')
        .insert({
          name: name.trim(),
          admission_number: admission_number.trim().toUpperCase(),
          hostel: hostel?.trim() || null,
          route: route?.trim() || null
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return createApiResponse(newStudent);
    } catch (error) {
      return handleApiError(error, 'Failed to create student');
    }
  });
}

// PUT /api/admin/students - Update a student
export async function PUT(request: NextRequest) {
  return withAuth(request, async (req) => {
    try {
      const body = await req.json();
      
      // Validate request body
      const validation = validateRequestBody<{
        id: string;
        name: string;
        admission_number: string;
        hostel?: string;
        route?: string;
      }>(body, ['id', 'name', 'admission_number']);

      if (!validation.isValid) {
        return NextResponse.json(
          { error: 'Validation failed', details: validation.errors },
          { status: 400 }
        );
      }

      const { id, name, admission_number, hostel, route } = body;
      const supabaseAdmin = createSupabaseAdmin();

      // Check if admission number already exists for a different student
      const { data: existingStudent } = await supabaseAdmin
        .from('students')
        .select('id')
        .eq('admission_number', admission_number)
        .neq('id', id)
        .single();

      if (existingStudent) {
        return NextResponse.json(
          { error: 'Another student with this admission number already exists' },
          { status: 409 }
        );
      }

      // Update student
      const { data: updatedStudent, error } = await supabaseAdmin
        .from('students')
        .update({
          name: name.trim(),
          admission_number: admission_number.trim().toUpperCase(),
          hostel: hostel?.trim() || null,
          route: route?.trim() || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      if (!updatedStudent) {
        return NextResponse.json(
          { error: 'Student not found' },
          { status: 404 }
        );
      }

      return createApiResponse(updatedStudent);
    } catch (error) {
      return handleApiError(error, 'Failed to update student');
    }
  });
}

// DELETE /api/admin/students - Delete a student
export async function DELETE(request: NextRequest) {
  return withAuth(request, async (req) => {
    try {
      const { searchParams } = new URL(req.url);
      const id = searchParams.get('id');

      if (!id) {
        return NextResponse.json(
          { error: 'Student ID is required' },
          { status: 400 }
        );
      }

      const supabaseAdmin = createSupabaseAdmin();

      // Delete student
      const { data: deletedStudent, error } = await supabaseAdmin
        .from('students')
        .delete()
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      if (!deletedStudent) {
        return NextResponse.json(
          { error: 'Student not found' },
          { status: 404 }
        );
      }

      return createApiResponse({ message: 'Student deleted successfully', student: deletedStudent });
    } catch (error) {
      return handleApiError(error, 'Failed to delete student');
    }
  });
}
