const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://uunrhhqghpcnwjatnoea.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV1bnJoaHFnaHBjbndqYXRub2VhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NDcwMjE3NywiZXhwIjoyMDcwMjc4MTc3fQ.WBcIJcFamu6IbrcDjBzyAoMR1YFZpAuxltAxJC0HuzM';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugStats() {
  console.log('=== DEBUGGING ADMIN STATISTICS ===\n');

  // 1. Check admin_settings table
  console.log('1. Admin Settings Table:');
  const { data: adminSettings, error: adminError } = await supabase
    .from('admin_settings')
    .select('*')
    .eq('id', 1)
    .single();
  
  if (adminError) {
    console.error('Error fetching admin settings:', adminError);
  } else {
    console.log('Admin Settings:', JSON.stringify(adminSettings, null, 2));
  }

  // 2. Count actual bookings
  console.log('\n2. Actual Booking Counts:');
  
  // Total bookings
  const { count: totalBookings, error: totalError } = await supabase
    .from('bookings')
    .select('*', { count: 'exact', head: true });
  
  console.log('Total bookings in database:', totalBookings);

  // Paid bookings
  const { count: paidBookings, error: paidError } = await supabase
    .from('bookings')
    .select('*', { count: 'exact', head: true })
    .eq('payment_status', true);
  
  console.log('Paid bookings in database:', paidBookings);

  // Unpaid bookings
  const { count: unpaidBookings, error: unpaidError } = await supabase
    .from('bookings')
    .select('*', { count: 'exact', head: true })
    .eq('payment_status', false);
  
  console.log('Unpaid bookings in database:', unpaidBookings);

  // 3. Check all bookings details
  console.log('\n3. All Bookings Details:');
  const { data: allBookings, error: bookingsError } = await supabase
    .from('bookings')
    .select('id, student_name, payment_status, created_at')
    .order('created_at', { ascending: false });
  
  if (bookingsError) {
    console.error('Error fetching bookings:', bookingsError);
  } else {
    console.log('All bookings:', JSON.stringify(allBookings, null, 2));
  }

  // 4. Test the database function
  console.log('\n4. Database Function Result:');
  const { data: functionResult, error: functionError } = await supabase
    .rpc('get_detailed_booking_statistics');
  
  if (functionError) {
    console.error('Error calling function:', functionError);
  } else {
    console.log('Function result:', JSON.stringify(functionResult, null, 2));
  }

  // 5. Check buses
  console.log('\n5. Buses Information:');
  const { data: buses, error: busesError } = await supabase
    .from('buses')
    .select('id, name, route_code, available_seats, is_active')
    .eq('is_active', true);
  
  if (busesError) {
    console.error('Error fetching buses:', busesError);
  } else {
    console.log('Active buses:', JSON.stringify(buses, null, 2));
    const totalAvailableSeats = buses?.reduce((sum, bus) => sum + (bus.available_seats || 0), 0) || 0;
    console.log('Total available seats:', totalAvailableSeats);
  }
}

debugStats().catch(console.error);
