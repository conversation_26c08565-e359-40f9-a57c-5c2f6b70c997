const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://uunrhhqghpcnwjatnoea.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV1bnJoaHFnaHBjbndqYXRub2VhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NDcwMjE3NywiZXhwIjoyMDcwMjc4MTc3fQ.WBcIJcFamu6IbrcDjBzyAoMR1YFZpAuxltAxJC0HuzM';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixStats() {
  console.log('=== FIXING ADMIN STATISTICS ===\n');

  // 1. Skip function creation for now, focus on data reconciliation
  console.log('1. Skipping function creation, focusing on data reconciliation...');

  // 2. Recalculate and update the admin_settings counters based on actual data
  console.log('\n2. Recalculating statistics from actual data...');

  // Count actual bookings
  const { count: totalBookings } = await supabase
    .from('bookings')
    .select('*', { count: 'exact', head: true });

  const { count: paidBookings } = await supabase
    .from('bookings')
    .select('*', { count: 'exact', head: true })
    .eq('payment_status', true);

  const { count: unpaidBookings } = await supabase
    .from('bookings')
    .select('*', { count: 'exact', head: true })
    .eq('payment_status', false);

  // Calculate revenue (assuming we have fare data)
  const { data: paidBookingsData } = await supabase
    .from('bookings')
    .select('fare')
    .eq('payment_status', true);

  const currentRevenue = paidBookingsData?.reduce((sum, booking) => sum + (booking.fare || 0), 0) || 0;

  console.log('Calculated values:');
  console.log('- Total bookings:', totalBookings);
  console.log('- Paid bookings:', paidBookings);
  console.log('- Unpaid bookings:', unpaidBookings);
  console.log('- Current revenue:', currentRevenue);

  // 3. Update admin_settings with correct values
  console.log('\n3. Updating admin_settings table...');
  
  const { error: updateError } = await supabase
    .from('admin_settings')
    .update({
      current_bookings: totalBookings,
      paid_bookings: paidBookings,
      unpaid_bookings: unpaidBookings,
      current_revenue: currentRevenue,
      updated_at: new Date().toISOString()
    })
    .eq('id', 1);

  if (updateError) {
    console.error('Error updating admin_settings:', updateError);
  } else {
    console.log('✓ Admin settings updated successfully');
  }

  // 4. Test the function
  console.log('\n4. Testing the fixed function...');
  const { data: functionResult, error: testError } = await supabase
    .rpc('get_detailed_booking_statistics');

  if (testError) {
    console.error('Error testing function:', testError);
  } else {
    console.log('✓ Function test result:', JSON.stringify(functionResult, null, 2));
  }

  // 5. Verify admin_settings
  console.log('\n5. Verifying admin_settings...');
  const { data: adminSettings, error: verifyError } = await supabase
    .from('admin_settings')
    .select('*')
    .eq('id', 1)
    .single();

  if (verifyError) {
    console.error('Error verifying admin_settings:', verifyError);
  } else {
    console.log('✓ Updated admin_settings:', JSON.stringify(adminSettings, null, 2));
  }
}

fixStats().catch(console.error);
