/*
  Fix Reset Functions - Emergency Database Update
  
  This script fixes the reset functionality by ensuring the correct
  version of reset_all_bookings() function is active and has proper
  permissions.
  
  Run this script in your Supabase SQL editor to fix the 500 error.
*/

-- First, ensure the archive_booking_round function exists and is correct
CREATE OR REPLACE FUNCTION archive_booking_round()
RETURNS VOID AS $$
DECLARE
  current_settings RECORD;
  total_paid_bookings INTEGER;
  total_unpaid_bookings INTEGER;
  total_all_bookings INTEGER;
  total_current_revenue DECIMAL(10,2);
BEGIN
  -- Get current admin settings
  SELECT go_date, return_date, current_revenue 
  INTO current_settings
  FROM admin_settings 
  WHERE id = 1;
  
  -- Get current booking stats
  SELECT 
    COALESCE(paid_bookings, 0),
    COALESCE(unpaid_bookings, 0),
    COALESCE(current_revenue, 0.00)
  INTO total_paid_bookings, total_unpaid_bookings, total_current_revenue
  FROM admin_settings 
  WHERE id = 1;
  
  -- Calculate total bookings (paid + unpaid)
  total_all_bookings := total_paid_bookings + total_unpaid_bookings;
  
  -- Only archive if there are actual bookings or revenue
  IF total_all_bookings > 0 OR total_current_revenue > 0 THEN
    INSERT INTO booking_rounds (
      go_date, 
      return_date, 
      total_bookings, 
      total_revenue, 
      reset_date
    ) VALUES (
      COALESCE(current_settings.go_date, CURRENT_DATE),
      COALESCE(current_settings.return_date, CURRENT_DATE + INTERVAL '1 day'),
      total_all_bookings,
      total_current_revenue,
      NOW()
    );
    
    RAISE NOTICE 'Archived booking round with % total bookings (% paid, % unpaid) and % revenue', 
                 total_all_bookings, total_paid_bookings, total_unpaid_bookings, total_current_revenue;
  ELSE
    RAISE NOTICE 'No bookings or revenue to archive, skipping archival';
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Now create the correct reset_all_bookings function
CREATE OR REPLACE FUNCTION reset_all_bookings()
RETURNS VOID AS $$
BEGIN
  -- Archive current booking round before resetting
  BEGIN
    PERFORM archive_booking_round();
  EXCEPTION
    WHEN undefined_function THEN
      RAISE NOTICE 'archive_booking_round() function not found, skipping archival';
    WHEN OTHERS THEN
      RAISE NOTICE 'Error archiving booking round: %', SQLERRM;
  END;

  -- Reset available seats for all active buses to their total seats
  UPDATE buses
  SET available_seats = total_seats,
      updated_at = NOW()
  WHERE is_active = true;

  -- Reset booking statistics in admin_settings
  UPDATE admin_settings
  SET current_bookings = 0,
      paid_bookings = 0,
      unpaid_bookings = 0,
      current_revenue = 0.00,
      updated_at = NOW()
  WHERE id = 1;

  -- Reset analytics_revenue table (if it exists)
  BEGIN
    DELETE FROM analytics_revenue;
  EXCEPTION
    WHEN undefined_table THEN
      RAISE NOTICE 'analytics_revenue table not found, skipping deletion';
    WHEN OTHERS THEN
      RAISE NOTICE 'Error clearing analytics_revenue: %', SQLERRM;
  END;

  -- Log the reset operation
  RAISE NOTICE 'All booking statistics, bus seats, revenue, and analytics have been reset successfully.';
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Error resetting bookings: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION archive_booking_round() TO authenticated;
GRANT EXECUTE ON FUNCTION reset_all_bookings() TO authenticated;

-- Test the function to make sure it works
DO $$
BEGIN
  RAISE NOTICE 'Testing reset_all_bookings function...';
  
  -- This will test if the function can be called without errors
  -- Comment out the next line if you don't want to actually reset data
  -- PERFORM reset_all_bookings();
  
  RAISE NOTICE 'Function test completed successfully';
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Function test failed: %', SQLERRM;
END;
$$;

-- Verify the functions exist
SELECT 
  routine_name,
  routine_type,
  security_type
FROM information_schema.routines 
WHERE routine_name IN ('reset_all_bookings', 'archive_booking_round')
  AND routine_schema = 'public';

-- Show current admin_settings for reference
SELECT 
  id,
  current_bookings,
  paid_bookings,
  unpaid_bookings,
  current_revenue,
  go_date,
  return_date
FROM admin_settings 
WHERE id = 1;
