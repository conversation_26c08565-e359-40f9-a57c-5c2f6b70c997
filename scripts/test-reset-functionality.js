#!/usr/bin/env node

/**
 * Test script for Reset Functionality
 * 
 * This script tests the complete reset workflow including:
 * 1. Archiving current data to booking_rounds
 * 2. Resetting admin_settings
 * 3. Resetting bus seats
 * 4. Clearing analytics_revenue
 */

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

/**
 * Make HTTP request with error handling
 */
async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    });

    const data = await response.json();
    
    return {
      status: response.status,
      ok: response.ok,
      data
    };
  } catch (error) {
    return {
      status: 0,
      ok: false,
      error: error.message
    };
  }
}

/**
 * Test the reset API endpoint
 */
async function testResetEndpoint() {
  console.log('🧪 Testing Reset API Endpoint...\n');
  
  console.log('📡 Making POST request to /api/admin/analytics/reset');
  
  const result = await makeRequest(`${BASE_URL}/api/admin/analytics/reset`, {
    method: 'POST',
    credentials: 'include'
  });
  
  console.log(`📊 Status: ${result.status}`);
  console.log(`✅ Success: ${result.ok}`);
  
  if (result.ok) {
    console.log('🎉 Reset successful!');
    console.log('📋 Response data:', JSON.stringify(result.data, null, 2));
    
    // Test if the data structure is correct
    if (result.data.success) {
      console.log('✅ Response has success field');
    }
    
    if (result.data.data) {
      console.log('✅ Response has data field');
      const data = result.data.data;
      
      if (data.message) {
        console.log(`📝 Message: ${data.message}`);
      }
      
      if (data.resetSeats !== undefined) {
        console.log(`🚌 Seats reset: ${data.resetSeats}`);
      }
      
      if (data.resetStatistics !== undefined) {
        console.log(`📊 Statistics reset: ${data.resetStatistics}`);
      }
    }
  } else {
    console.log('❌ Reset failed!');
    console.log('💬 Error:', result.data?.error || result.error || 'Unknown error');
    
    if (result.data?.details) {
      console.log('🔍 Details:', result.data.details);
    }
  }
  
  return result;
}

/**
 * Test the booking rounds endpoint to verify archival
 */
async function testBookingRoundsEndpoint() {
  console.log('\n🧪 Testing Booking Rounds Endpoint...\n');
  
  console.log('📡 Making GET request to /api/admin/reports/rounds');
  
  const result = await makeRequest(`${BASE_URL}/api/admin/reports/rounds`, {
    method: 'GET',
    credentials: 'include'
  });
  
  console.log(`📊 Status: ${result.status}`);
  console.log(`✅ Success: ${result.ok}`);
  
  if (result.ok && result.data.success) {
    const rounds = result.data.data.rounds;
    console.log(`📈 Found ${rounds.length} booking rounds`);
    
    if (rounds.length > 0) {
      console.log('📋 Latest round:');
      const latest = rounds[0];
      console.log(`   - ID: ${latest.id}`);
      console.log(`   - Go Date: ${latest.goDate}`);
      console.log(`   - Return Date: ${latest.returnDate}`);
      console.log(`   - Total Bookings: ${latest.totalBookings}`);
      console.log(`   - Total Revenue: ${latest.totalRevenue}`);
      console.log(`   - Reset Date: ${latest.resetDate}`);
    } else {
      console.log('📝 No booking rounds found (this is normal if no data was archived)');
    }
  } else {
    console.log('❌ Failed to fetch booking rounds');
    console.log('💬 Error:', result.data?.error || result.error || 'Unknown error');
  }
  
  return result;
}

/**
 * Test admin settings to verify reset
 */
async function testAdminSettings() {
  console.log('\n🧪 Testing Admin Settings After Reset...\n');
  
  console.log('📡 Making GET request to /api/admin/analytics/detailed-stats');
  
  const result = await makeRequest(`${BASE_URL}/api/admin/analytics/detailed-stats`, {
    method: 'GET',
    credentials: 'include'
  });
  
  console.log(`📊 Status: ${result.status}`);
  console.log(`✅ Success: ${result.ok}`);
  
  if (result.ok && result.data.success) {
    const stats = result.data.data;
    console.log('📋 Current statistics after reset:');
    console.log(`   - Current Bookings: ${stats.currentBookings}`);
    console.log(`   - Paid Bookings: ${stats.paidBookings}`);
    console.log(`   - Unpaid Bookings: ${stats.unpaidBookings}`);
    console.log(`   - Current Revenue: ${stats.currentRevenue}`);
    
    // Verify all values are 0
    const allZero = stats.currentBookings === 0 && 
                   stats.paidBookings === 0 && 
                   stats.unpaidBookings === 0 && 
                   stats.currentRevenue === 0;
    
    if (allZero) {
      console.log('✅ All statistics correctly reset to 0');
    } else {
      console.log('⚠️  Some statistics were not reset to 0');
    }
  } else {
    console.log('❌ Failed to fetch admin settings');
    console.log('💬 Error:', result.data?.error || result.error || 'Unknown error');
  }
  
  return result;
}

/**
 * Main test function
 */
async function runResetTests() {
  console.log('🚀 Starting Reset Functionality Tests');
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log('=' .repeat(50));

  const results = [];

  // Test 1: Reset endpoint
  const resetResult = await testResetEndpoint();
  results.push({ test: 'Reset Endpoint', result: resetResult });

  // Test 2: Booking rounds (archival verification)
  const roundsResult = await testBookingRoundsEndpoint();
  results.push({ test: 'Booking Rounds', result: roundsResult });

  // Test 3: Admin settings (reset verification)
  const settingsResult = await testAdminSettings();
  results.push({ test: 'Admin Settings', result: settingsResult });

  // Summary
  console.log('\n' + '=' .repeat(50));
  console.log('📋 Test Summary');
  console.log('=' .repeat(50));

  let passed = 0;
  let failed = 0;

  results.forEach(({ test, result }) => {
    const status = result.ok ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${test}`);
    
    if (result.ok) {
      passed++;
    } else {
      failed++;
    }
  });

  console.log(`\n📊 Results: ${passed} passed, ${failed} failed`);
  
  if (failed === 0) {
    console.log('🎉 All reset functionality tests passed!');
  } else {
    console.log('⚠️  Some tests failed. Check the output above for details.');
  }

  return { passed, failed, results };
}

// Run tests if this script is executed directly
if (require.main === module) {
  runResetTests().catch(console.error);
}

module.exports = { runResetTests, testResetEndpoint, testBookingRoundsEndpoint, testAdminSettings };
