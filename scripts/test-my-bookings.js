#!/usr/bin/env node

/**
 * Test script for My Bookings feature
 * Tests the API endpoint and validates responses
 */

const BASE_URL = 'http://localhost:3000';

async function testBookingSearch() {
  console.log('🧪 Testing My Bookings Feature\n');

  const tests = [
    {
      name: 'Valid admission number format',
      admissionNumber: '22CS001',
      expectedStatus: 200
    },
    {
      name: 'Invalid admission number format (too short)',
      admissionNumber: '22CS01',
      expectedStatus: 400
    },
    {
      name: 'Invalid admission number format (wrong pattern)',
      admissionNumber: 'ABC1234',
      expectedStatus: 400
    },
    {
      name: 'Empty admission number',
      admissionNumber: '',
      expectedStatus: 400
    }
  ];

  for (const test of tests) {
    console.log(`\n📋 Test: ${test.name}`);
    console.log(`   Input: "${test.admissionNumber}"`);
    
    try {
      const response = await fetch(`${BASE_URL}/api/bookings/search?admission_number=${encodeURIComponent(test.admissionNumber)}`);
      const data = await response.json();
      
      console.log(`   Status: ${response.status} (expected: ${test.expectedStatus})`);
      
      if (response.status === test.expectedStatus) {
        console.log('   ✅ PASS');
      } else {
        console.log('   ❌ FAIL');
      }
      
    } catch (error) {
      console.log(`   ❌ ERROR: ${error.message}`);
    }
  }
}

async function main() {
  try {
    await testBookingSearch();
    
    console.log('\n\n🎉 Test completed!');
    console.log('\nTo run the application:');
    console.log('1. Start the development server: npm run dev');
    console.log('2. Visit http://localhost:3000');
    console.log('3. Click "My Bookings" to test the feature');
    
  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  }
}

main(); 