#!/usr/bin/env node

/**
 * Test script to verify fare integration in bookings
 * 
 * This script:
 * 1. Checks that route_stops table has fare data
 * 2. Creates a test booking with fare retrieval
 * 3. Verifies the booking includes the correct fare
 * 4. Tests the booking statistics API with actual fare data
 */

const { createClient } = require('@supabase/supabase-js');

// Configuration - replace with your actual values
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('Missing required environment variables:');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function testFareIntegration() {
  console.log('🧪 Testing fare integration in bookings...\n');

  try {
    // Step 1: Check route_stops table for fare data
    console.log('1. Checking route_stops table for fare data...');
    const { data: routeStops, error: routeStopsError } = await supabase
      .from('route_stops')
      .select('route_code, stop_name, fare')
      .limit(5);

    if (routeStopsError) {
      throw new Error(`Failed to fetch route_stops: ${routeStopsError.message}`);
    }

    if (!routeStops || routeStops.length === 0) {
      throw new Error('No route_stops data found. Please ensure the table is populated.');
    }

    console.log('✅ Route_stops data found:');
    routeStops.forEach(stop => {
      console.log(`   ${stop.route_code} -> ${stop.stop_name}: ₹${stop.fare}`);
    });

    // Step 2: Set up admin settings
    console.log('\n2. Setting up admin settings...');
    const { error: settingsError } = await supabase
      .from('admin_settings')
      .upsert({
        id: 1,
        booking_enabled: true,
        go_date: '2024-01-15',
        return_date: '2024-01-20',
        updated_at: new Date().toISOString()
      });

    if (settingsError) {
      throw new Error(`Failed to update admin settings: ${settingsError.message}`);
    }
    console.log('✅ Admin settings updated successfully');

    // Step 3: Test booking creation with fare
    console.log('\n3. Testing booking creation with fare...');
    const testRoute = routeStops[0].route_code;
    const testDestination = routeStops[0].stop_name;
    const expectedFare = routeStops[0].fare;

    console.log(`   Testing with route: ${testRoute}, destination: ${testDestination}, expected fare: ₹${expectedFare}`);

    // Create booking via API
    const bookingResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/bookings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        studentName: 'Test Student',
        admissionNumber: 'TEST002',
        busRoute: testRoute,
        destination: testDestination,
        paymentStatus: false,
        timestamp: new Date().toISOString()
      })
    });

    if (!bookingResponse.ok) {
      const errorData = await bookingResponse.json();
      throw new Error(`Booking creation failed: ${errorData.error} - ${errorData.details}`);
    }

    const bookingData = await bookingResponse.json();
    console.log('✅ Booking created successfully via API');

    // Step 4: Verify the booking includes the correct fare
    console.log('\n4. Verifying fare in booking...');
    const { data: booking, error: fetchError } = await supabase
      .from('bookings')
      .select('*')
      .eq('admission_number', 'TEST002')
      .single();

    if (fetchError || !booking) {
      throw new Error(`Failed to fetch booking: ${fetchError?.message || 'Booking not found'}`);
    }

    console.log('Booking data:', {
      id: booking.id,
      student_name: booking.student_name,
      bus_route: booking.bus_route,
      destination: booking.destination,
      fare: booking.fare,
      payment_status: booking.payment_status
    });

    if (booking.fare === expectedFare) {
      console.log('✅ Fare is correctly stored in the booking!');
      console.log(`   Expected: ₹${expectedFare}, Actual: ₹${booking.fare}`);
    } else {
      console.log('❌ Fare mismatch in booking');
      console.log(`   Expected: ₹${expectedFare}, Actual: ₹${booking.fare}`);
    }

    // Step 5: Test booking statistics with actual fare data
    console.log('\n5. Testing booking statistics with fare data...');
    const statsResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/admin/bookings/stats`, {
      headers: {
        'Authorization': `Bearer ${process.env.ADMIN_TOKEN || 'test-token'}`
      }
    });

    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log('✅ Booking statistics retrieved successfully');
      console.log('Statistics data:', {
        totalBookings: statsData.data.totalBookings,
        paidBookings: statsData.data.paidBookings,
        totalRevenue: statsData.data.totalRevenue,
        estimatedRevenue: statsData.data.estimatedRevenue
      });
    } else {
      console.log('⚠️  Could not test booking statistics (may require authentication)');
    }

    // Step 6: Clean up test data
    console.log('\n6. Cleaning up test data...');
    const { error: deleteError } = await supabase
      .from('bookings')
      .delete()
      .eq('admission_number', 'TEST002');

    if (deleteError) {
      console.log('⚠️  Warning: Failed to clean up test booking:', deleteError.message);
    } else {
      console.log('✅ Test booking cleaned up successfully');
    }

    console.log('\n🎉 Fare integration test completed successfully!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testFareIntegration(); 