const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  console.error('Please ensure NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testCurrentRevenue() {
  console.log('🧪 Testing Current Revenue Implementation...\n');

  try {
    // Test 1: Check if current_revenue column exists
    console.log('1. Checking if current_revenue column exists...');
    const { data: adminSettings, error: adminError } = await supabase
      .from('admin_settings')
      .select('current_revenue')
      .eq('id', 1)
      .single();

    if (adminError) {
      console.error('❌ Error fetching admin_settings:', adminError);
      return;
    }

    if (adminSettings.current_revenue !== null && adminSettings.current_revenue !== undefined) {
      console.log('✅ current_revenue column exists in admin_settings table');
      console.log(`   Current revenue: ₹${adminSettings.current_revenue}`);
    } else {
      console.log('❌ current_revenue column missing from admin_settings table');
      return;
    }

    // Test 2: Check if handle_new_booking function includes revenue logic
    console.log('\n2. Checking handle_new_booking function...');
    const { data: functionData, error: functionError } = await supabase
      .rpc('handle_new_booking');

    if (functionError && functionError.message.includes('function')) {
      console.log('✅ handle_new_booking function exists (trigger function)');
    } else {
      console.log('⚠️  handle_new_booking function check inconclusive (this is normal for trigger functions)');
    }

    // Test 3: Check if reset_all_bookings function includes revenue reset
    console.log('\n3. Checking reset_all_bookings function...');
    const { data: resetData, error: resetError } = await supabase
      .rpc('reset_all_bookings');

    if (resetError) {
      console.log('❌ Error calling reset_all_bookings function:', resetError.message);
    } else {
      console.log('✅ reset_all_bookings function works and includes revenue reset');
    }

    // Test 4: Check if get_detailed_booking_statistics includes revenue
    console.log('\n4. Checking get_detailed_booking_statistics function...');
    const { data: statsData, error: statsError } = await supabase
      .rpc('get_detailed_booking_statistics');

    if (statsError) {
      console.log('❌ Error calling get_detailed_booking_statistics:', statsError.message);
    } else if (statsData && statsData.length > 0) {
      const stats = statsData[0];
      if (stats.current_revenue !== null && stats.current_revenue !== undefined) {
        console.log('✅ get_detailed_booking_statistics includes current_revenue');
        console.log(`   Current revenue from function: ₹${stats.current_revenue}`);
      } else {
        console.log('❌ get_detailed_booking_statistics missing current_revenue');
      }
    } else {
      console.log('❌ No data returned from get_detailed_booking_statistics');
    }

    // Test 5: Test booking creation with revenue tracking
    console.log('\n5. Testing booking creation with revenue tracking...');
    
    // Get current revenue before test
    const { data: beforeData } = await supabase
      .from('admin_settings')
      .select('current_revenue, current_bookings')
      .eq('id', 1)
      .single();

    const beforeRevenue = beforeData?.current_revenue || 0;
    const beforeBookings = beforeData?.current_bookings || 0;

    console.log(`   Revenue before test: ₹${beforeRevenue}`);
    console.log(`   Bookings before test: ${beforeBookings}`);

    // Create a test booking
    const testBooking = {
      admission_number: 'TEST001',
      student_name: 'Test Student',
      bus_route: 'route-1',
      destination: 'Test Destination',
      payment_status: true,
      fare: 150.00,
      bus_name: 'Test Bus',
      go_date: '2024-01-20',
      return_date: '2024-01-25'
    };

    const { data: bookingData, error: bookingError } = await supabase
      .from('bookings')
      .insert(testBooking)
      .select()
      .single();

    if (bookingError) {
      console.log('❌ Error creating test booking:', bookingError.message);
    } else {
      console.log('✅ Test booking created successfully');

      // Check if revenue was incremented
      const { data: afterData } = await supabase
        .from('admin_settings')
        .select('current_revenue, current_bookings')
        .eq('id', 1)
        .single();

      const afterRevenue = afterData?.current_revenue || 0;
      const afterBookings = afterData?.current_bookings || 0;

      console.log(`   Revenue after test: ₹${afterRevenue}`);
      console.log(`   Bookings after test: ${afterBookings}`);

      if (afterRevenue > beforeRevenue) {
        console.log('✅ Revenue was incremented correctly');
        console.log(`   Revenue increase: ₹${afterRevenue - beforeRevenue}`);
      } else {
        console.log('❌ Revenue was not incremented');
      }

      if (afterBookings > beforeBookings) {
        console.log('✅ Bookings counter was incremented correctly');
        console.log(`   Bookings increase: ${afterBookings - beforeBookings}`);
      } else {
        console.log('❌ Bookings counter was not incremented');
      }

      // Clean up test booking
      const { error: deleteError } = await supabase
        .from('bookings')
        .delete()
        .eq('admission_number', 'TEST001');

      if (deleteError) {
        console.log('⚠️  Warning: Could not clean up test booking:', deleteError.message);
      } else {
        console.log('✅ Test booking cleaned up');
      }
    }

    // Test 6: Test API endpoints
    console.log('\n6. Testing API endpoints...');
    
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL.replace('.supabase.co', '.supabase.co')}/rest/v1/rpc/get_detailed_booking_statistics`, {
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const apiData = await response.json();
        if (apiData && apiData.length > 0 && apiData[0].current_revenue !== undefined) {
          console.log('✅ API endpoint includes current_revenue');
          console.log(`   API revenue: ₹${apiData[0].current_revenue}`);
        } else {
          console.log('❌ API endpoint missing current_revenue');
        }
      } else {
        console.log('❌ API endpoint test failed:', response.status);
      }
    } catch (apiError) {
      console.log('⚠️  API endpoint test skipped (requires running server)');
    }

    console.log('\n🎉 Current Revenue Implementation Test Complete!');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testCurrentRevenue(); 