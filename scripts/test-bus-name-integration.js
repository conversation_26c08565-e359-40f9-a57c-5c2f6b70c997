const { createClient } = require('@supabase/supabase-js');

// Test configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables');
  console.error('Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function testBusNameIntegration() {
  console.log('🚌 Testing Bus Name Integration...\n');

  try {
    // Step 1: Check if bus_name column exists
    console.log('1. Checking if bus_name column exists...');
    const { data: columns, error: columnError } = await supabaseAdmin
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'bookings')
      .eq('column_name', 'bus_name');

    if (columnError) {
      console.error('❌ Error checking columns:', columnError);
      return;
    }

    if (columns.length === 0) {
      console.error('❌ bus_name column not found. Please run the migration first.');
      return;
    }
    console.log('✅ bus_name column exists\n');

    // Step 2: Check if buses table has data
    console.log('2. Checking buses table data...');
    const { data: buses, error: busesError } = await supabaseAdmin
      .from('buses')
      .select('name, route_code')
      .eq('is_active', true)
      .limit(5);

    if (busesError) {
      console.error('❌ Error fetching buses:', busesError);
      return;
    }

    if (!buses || buses.length === 0) {
      console.error('❌ No buses found in the database');
      return;
    }

    console.log('✅ Found buses:', buses.map(b => `${b.name} (${b.route_code})`).join(', '));
    console.log('');

    // Step 3: Test booking creation with bus name
    console.log('3. Testing booking creation with bus name...');
    const testBus = buses[0];
    const testBooking = {
      studentName: 'Test Student',
      admissionNumber: '22CS999',
      busRoute: testBus.route_code,
      destination: 'Test Destination',
      paymentStatus: false,
      timestamp: new Date().toISOString()
    };

    console.log('Creating test booking:', {
      ...testBooking,
      expectedBusName: testBus.name
    });

    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/bookings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testBooking)
    });

    const result = await response.json();
    
    if (!response.ok) {
      console.error('❌ Booking creation failed:', result);
      return;
    }

    console.log('✅ Booking created successfully');
    console.log('Booking data:', result.booking);
    console.log('');

    // Step 4: Verify bus name was stored correctly
    console.log('4. Verifying bus name was stored correctly...');
    const { data: createdBooking, error: fetchError } = await supabaseAdmin
      .from('bookings')
      .select('*')
      .eq('admission_number', testBooking.admissionNumber)
      .single();

    if (fetchError) {
      console.error('❌ Error fetching created booking:', fetchError);
      return;
    }

    if (createdBooking.bus_name === testBus.name) {
      console.log('✅ Bus name stored correctly:', createdBooking.bus_name);
    } else {
      console.error('❌ Bus name mismatch:');
      console.error('  Expected:', testBus.name);
      console.error('  Actual:', createdBooking.bus_name);
    }
    console.log('');

    // Step 5: Test booking search includes bus name
    console.log('5. Testing booking search includes bus name...');
    const searchResponse = await fetch(
      `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/bookings/search?admission_number=${testBooking.admissionNumber}`
    );

    const searchResult = await searchResponse.json();
    
    if (!searchResponse.ok) {
      console.error('❌ Booking search failed:', searchResult);
      return;
    }

    if (searchResult.data.bookings.length > 0) {
      const foundBooking = searchResult.data.bookings[0];
      if (foundBooking.bus_name) {
        console.log('✅ Booking search includes bus name:', foundBooking.bus_name);
      } else {
        console.error('❌ Booking search missing bus name');
      }
    } else {
      console.error('❌ No bookings found in search');
    }
    console.log('');

    // Step 6: Clean up test data
    console.log('6. Cleaning up test data...');
    const { error: deleteError } = await supabaseAdmin
      .from('bookings')
      .delete()
      .eq('admission_number', testBooking.admissionNumber);

    if (deleteError) {
      console.error('❌ Error cleaning up test data:', deleteError);
    } else {
      console.log('✅ Test data cleaned up');
    }

    console.log('\n🎉 Bus Name Integration Test Completed Successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testBusNameIntegration(); 