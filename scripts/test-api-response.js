#!/usr/bin/env node

/**
 * Test script to verify API response structure
 */

const BASE_URL = 'http://localhost:3000';

async function testApiResponse() {
  console.log('🧪 Testing API Response Structure\n');

  const testAdmissionNumber = '24CS094'; // Use the one from your example

  try {
    console.log(`Testing with admission number: ${testAdmissionNumber}`);
    
    const response = await fetch(`${BASE_URL}/api/bookings/search?admission_number=${testAdmissionNumber}`);
    const data = await response.json();
    
    console.log('\n📋 Full API Response:');
    console.log(JSON.stringify(data, null, 2));
    
    console.log('\n🔍 Response Structure Analysis:');
    console.log(`- Status: ${response.status}`);
    console.log(`- Success: ${data.success}`);
    console.log(`- Has data property: ${!!data.data}`);
    console.log(`- Has bookings array: ${!!data.data?.bookings}`);
    console.log(`- Bookings count: ${data.data?.bookings?.length || 0}`);
    console.log(`- Total count: ${data.data?.count || 0}`);
    
    if (data.data?.bookings) {
      console.log('\n📊 First Booking Details:');
      const firstBooking = data.data.bookings[0];
      console.log(`- ID: ${firstBooking.id}`);
      console.log(`- Student Name: ${firstBooking.student_name}`);
      console.log(`- Admission Number: ${firstBooking.admission_number}`);
      console.log(`- Bus Route: ${firstBooking.bus_route}`);
      console.log(`- Destination: ${firstBooking.destination}`);
      console.log(`- Go Date: ${firstBooking.go_date}`);
      console.log(`- Return Date: ${firstBooking.return_date}`);
      console.log(`- Fare: ${firstBooking.fare}`);
      console.log(`- Payment Status: ${firstBooking.payment_status}`);
      console.log(`- Created At: ${firstBooking.created_at}`);
    }
    
    console.log('\n✅ API Response Structure Test Complete');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch(`${BASE_URL}/api/booking-status`);
    return response.ok;
  } catch {
    return false;
  }
}

// Run test
checkServer().then(isRunning => {
  if (isRunning) {
    testApiResponse();
  } else {
    console.log('❌ Server not running. Please start the development server first:');
    console.log('   npm run dev');
    process.exit(1);
  }
}); 