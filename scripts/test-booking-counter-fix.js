#!/usr/bin/env node

/**
 * Test Script: Booking Counter Fix Verification
 * 
 * This script tests that the booking counter (current_bookings) is incremented
 * by exactly 1 for both online and upfront ticket bookings after the fix.
 * 
 * Prerequisites:
 * - Supabase project must be running
 * - Database migrations must be applied
 * - Test data should be available
 */

const { createClient } = require('@supabase/supabase-js');

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function getCurrentBookingsCount() {
  const { data, error } = await supabase
    .from('admin_settings')
    .select('current_bookings')
    .eq('id', 1)
    .single();
  
  if (error) {
    throw new Error(`Failed to get current bookings count: ${error.message}`);
  }
  
  return data.current_bookings || 0;
}

async function createTestBooking(paymentStatus, bookingType) {
  const testBooking = {
    admission_number: `TEST_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`,
    student_name: `Test Student ${bookingType}`,
    bus_route: 'TEST_ROUTE',
    destination: 'Test Destination',
    payment_status: paymentStatus,
    created_at: new Date().toISOString(),
    razorpay_payment_id: paymentStatus ? 'test_payment_id' : null,
    razorpay_order_id: paymentStatus ? 'test_order_id' : null,
    razorpay_signature: paymentStatus ? 'test_signature' : null,
  };

  const { data, error } = await supabase
    .from('bookings')
    .insert(testBooking)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create test booking: ${error.message}`);
  }

  return data;
}

async function cleanupTestBookings() {
  const { error } = await supabase
    .from('bookings')
    .delete()
    .like('admission_number', 'TEST_%');

  if (error) {
    console.warn('⚠️  Warning: Failed to cleanup test bookings:', error.message);
  } else {
    console.log('🧹 Cleaned up test bookings');
  }
}

async function resetBookingCounter() {
  const { error } = await supabase
    .from('admin_settings')
    .update({ current_bookings: 0 })
    .eq('id', 1);

  if (error) {
    throw new Error(`Failed to reset booking counter: ${error.message}`);
  }
  
  console.log('🔄 Reset booking counter to 0');
}

async function testBookingCounter() {
  console.log('🧪 Testing Booking Counter Fix...\n');

  try {
    // Step 1: Reset the booking counter to a known state
    await resetBookingCounter();
    
    // Step 2: Get initial count
    const initialCount = await getCurrentBookingsCount();
    console.log(`📊 Initial booking count: ${initialCount}`);

    // Step 3: Test online booking (payment_status = true)
    console.log('\n🔵 Testing Online Booking (payment_status = true)...');
    const beforeOnlineCount = await getCurrentBookingsCount();
    await createTestBooking(true, 'ONLINE');
    const afterOnlineCount = await getCurrentBookingsCount();
    const onlineIncrement = afterOnlineCount - beforeOnlineCount;
    
    console.log(`   Before: ${beforeOnlineCount}`);
    console.log(`   After:  ${afterOnlineCount}`);
    console.log(`   Increment: ${onlineIncrement}`);
    
    if (onlineIncrement === 1) {
      console.log('   ✅ Online booking correctly incremented by 1');
    } else {
      console.log(`   ❌ Online booking incorrectly incremented by ${onlineIncrement} (expected 1)`);
      throw new Error(`Online booking increment test failed: got ${onlineIncrement}, expected 1`);
    }

    // Step 4: Test upfront booking (payment_status = false)
    console.log('\n🟡 Testing Upfront Booking (payment_status = false)...');
    const beforeUpfrontCount = await getCurrentBookingsCount();
    await createTestBooking(false, 'UPFRONT');
    const afterUpfrontCount = await getCurrentBookingsCount();
    const upfrontIncrement = afterUpfrontCount - beforeUpfrontCount;
    
    console.log(`   Before: ${beforeUpfrontCount}`);
    console.log(`   After:  ${afterUpfrontCount}`);
    console.log(`   Increment: ${upfrontIncrement}`);
    
    if (upfrontIncrement === 1) {
      console.log('   ✅ Upfront booking correctly incremented by 1');
    } else {
      console.log(`   ❌ Upfront booking incorrectly incremented by ${upfrontIncrement} (expected 1)`);
      throw new Error(`Upfront booking increment test failed: got ${upfrontIncrement}, expected 1`);
    }

    // Step 5: Test multiple bookings to ensure consistency
    console.log('\n🔄 Testing Multiple Bookings...');
    const beforeMultipleCount = await getCurrentBookingsCount();
    
    // Create 3 more bookings (2 online, 1 upfront)
    await createTestBooking(true, 'ONLINE_2');
    await createTestBooking(false, 'UPFRONT_2');
    await createTestBooking(true, 'ONLINE_3');
    
    const afterMultipleCount = await getCurrentBookingsCount();
    const multipleIncrement = afterMultipleCount - beforeMultipleCount;
    
    console.log(`   Before: ${beforeMultipleCount}`);
    console.log(`   After:  ${afterMultipleCount}`);
    console.log(`   Total increment: ${multipleIncrement}`);
    
    if (multipleIncrement === 3) {
      console.log('   ✅ Multiple bookings correctly incremented by 3');
    } else {
      console.log(`   ❌ Multiple bookings incorrectly incremented by ${multipleIncrement} (expected 3)`);
      throw new Error(`Multiple bookings increment test failed: got ${multipleIncrement}, expected 3`);
    }

    // Step 6: Final verification
    const finalCount = await getCurrentBookingsCount();
    console.log(`\n📊 Final booking count: ${finalCount}`);
    
    if (finalCount === 5) { // 1 + 1 + 3 = 5 total test bookings
      console.log('✅ All tests passed! Booking counter is working correctly.');
      console.log('\n🎉 Summary:');
      console.log('   - Online bookings increment by 1 ✅');
      console.log('   - Upfront bookings increment by 1 ✅');
      console.log('   - Multiple bookings work consistently ✅');
      console.log('   - No double increment issues ✅');
    } else {
      throw new Error(`Final count verification failed: got ${finalCount}, expected 5`);
    }

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  } finally {
    // Cleanup test data
    await cleanupTestBookings();
  }
}

// Run the test
testBookingCounter()
  .then(() => {
    console.log('\n✨ All tests completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Test suite failed:', error);
    process.exit(1);
  }); 