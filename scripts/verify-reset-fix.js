#!/usr/bin/env node

/**
 * Verification Script for Reset Functionality Fix
 * 
 * This script tests the reset API endpoint to verify the 500 error is resolved.
 * Run this after applying the migration to confirm the fix works.
 */

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    });

    const data = await response.json();
    
    return {
      status: response.status,
      ok: response.ok,
      data
    };
  } catch (error) {
    return {
      status: 0,
      ok: false,
      error: error.message
    };
  }
}

async function testResetEndpoint() {
  console.log('🔧 Testing Reset API Endpoint Fix...\n');
  
  console.log(`📡 Making POST request to ${BASE_URL}/api/admin/analytics/reset`);
  
  const result = await makeRequest(`${BASE_URL}/api/admin/analytics/reset`, {
    method: 'POST',
    credentials: 'include'
  });
  
  console.log(`📊 Status Code: ${result.status}`);
  console.log(`✅ Success: ${result.ok}`);
  
  if (result.ok) {
    console.log('🎉 SUCCESS: Reset endpoint is working!');
    console.log('📋 Response:', JSON.stringify(result.data, null, 2));
    
    // Verify response structure
    if (result.data.success) {
      console.log('✅ Response has correct success field');
    }
    
    if (result.data.data && result.data.data.message) {
      console.log(`📝 Message: ${result.data.data.message}`);
    }
    
    return true;
  } else {
    console.log('❌ FAILED: Reset endpoint still has issues');
    console.log(`💬 Error: ${result.data?.error || result.error || 'Unknown error'}`);
    
    if (result.status === 500) {
      console.log('🚨 Still getting 500 error - migration may not have been applied');
    } else if (result.status === 401) {
      console.log('🔐 Authentication required - this is expected if not logged in');
    }
    
    return false;
  }
}

async function testBookingRounds() {
  console.log('\n📈 Testing Booking Rounds Endpoint...\n');
  
  const result = await makeRequest(`${BASE_URL}/api/admin/reports/rounds`, {
    method: 'GET',
    credentials: 'include'
  });
  
  console.log(`📊 Status Code: ${result.status}`);
  
  if (result.ok && result.data.success) {
    const rounds = result.data.data.rounds;
    console.log(`✅ Found ${rounds.length} booking rounds`);
    
    if (rounds.length > 0) {
      console.log('📋 Latest round data structure looks good');
    }
    
    return true;
  } else {
    console.log('⚠️  Booking rounds endpoint has issues');
    return false;
  }
}

async function runVerification() {
  console.log('🚀 Reset Functionality Verification');
  console.log(`🌐 Base URL: ${BASE_URL}`);
  console.log('=' .repeat(50));

  let allPassed = true;

  // Test 1: Reset endpoint
  const resetPassed = await testResetEndpoint();
  allPassed = allPassed && resetPassed;

  // Test 2: Booking rounds (if reset worked)
  if (resetPassed) {
    const roundsPassed = await testBookingRounds();
    allPassed = allPassed && roundsPassed;
  }

  // Summary
  console.log('\n' + '=' .repeat(50));
  console.log('📋 Verification Summary');
  console.log('=' .repeat(50));

  if (allPassed) {
    console.log('🎉 ALL TESTS PASSED!');
    console.log('✅ Reset functionality is working correctly');
    console.log('✅ The 500 error has been resolved');
    console.log('\n📝 Next steps:');
    console.log('1. Test the reset button in the admin dashboard');
    console.log('2. Verify data is archived to booking_rounds table');
    console.log('3. Check that all statistics are reset to zero');
  } else {
    console.log('❌ SOME TESTS FAILED');
    console.log('\n🔧 Troubleshooting steps:');
    console.log('1. Ensure the migration has been applied to your database');
    console.log('2. Check that you are authenticated as an admin user');
    console.log('3. Verify your environment variables are correct');
    console.log('4. Check the server logs for detailed error messages');
  }

  return allPassed;
}

// Run verification if this script is executed directly
if (require.main === module) {
  runVerification()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Verification failed with error:', error);
      process.exit(1);
    });
}

module.exports = { runVerification, testResetEndpoint, testBookingRounds };
