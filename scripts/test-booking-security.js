#!/usr/bin/env node

/**
 * Test Script: Booking Security Validation
 * 
 * This script tests the critical security fix for the booking system.
 * It verifies that when booking is disabled, no bookings can be created
 * through any API endpoint, even with direct API calls.
 */

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

async function testBookingSecurity() {
  console.log('🔒 Testing Booking Security Fix\n');
  console.log('This test verifies that booking is properly blocked when disabled\n');

  // Test data
  const testBookingData = {
    studentName: 'Test Student',
    admissionNumber: 'TEST001',
    busRoute: 'TEST_ROUTE',
    destination: 'Test Destination',
    paymentStatus: false,
    timestamp: new Date().toISOString(),
    razorpay_payment_id: null,
    razorpay_order_id: null,
    razorpay_signature: null,
  };

  const testPaymentOrderData = {
    amount: 5000, // 50 rupees in paise
    currency: 'INR',
    receipt: `test_booking_security_${Date.now()}`,
  };

  console.log('📋 Test Cases:');
  console.log('1. Test booking creation when booking is disabled');
  console.log('2. Test payment order creation when booking is disabled');
  console.log('3. Test booking status endpoint');
  console.log('');

  try {
    // Test 1: Check current booking status
    console.log('🔍 Test 1: Checking current booking status...');
    const statusResponse = await fetch(`${BASE_URL}/api/booking-status`);
    const statusData = await statusResponse.json();
    
    console.log(`   Status: ${statusResponse.status}`);
    console.log(`   Booking enabled: ${statusData.enabled}`);
    console.log('');

    // Test 2: Attempt to create booking (should fail if disabled)
    console.log('🚫 Test 2: Attempting to create booking...');
    const bookingResponse = await fetch(`${BASE_URL}/api/bookings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testBookingData),
    });

    const bookingData = await bookingResponse.json();
    
    console.log(`   Status: ${bookingResponse.status}`);
    if (bookingResponse.status === 403) {
      console.log('   ✅ SUCCESS: Booking correctly blocked when disabled');
      console.log(`   Error: ${bookingData.error}`);
    } else if (bookingResponse.status === 200) {
      console.log('   ❌ FAILURE: Booking allowed when it should be blocked');
    } else {
      console.log(`   ⚠️  UNEXPECTED: Status ${bookingResponse.status}`);
      console.log(`   Response: ${JSON.stringify(bookingData)}`);
    }
    console.log('');

    // Test 3: Attempt to create payment order (should fail if disabled)
    console.log('💳 Test 3: Attempting to create payment order...');
    const paymentResponse = await fetch(`${BASE_URL}/api/payment/order`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPaymentOrderData),
    });

    const paymentData = await paymentResponse.json();
    
    console.log(`   Status: ${paymentResponse.status}`);
    if (paymentResponse.status === 403) {
      console.log('   ✅ SUCCESS: Payment order correctly blocked when booking disabled');
      console.log(`   Error: ${paymentData.error}`);
    } else if (paymentResponse.status === 200) {
      console.log('   ❌ FAILURE: Payment order allowed when it should be blocked');
    } else {
      console.log(`   ⚠️  UNEXPECTED: Status ${paymentResponse.status}`);
      console.log(`   Response: ${JSON.stringify(paymentData)}`);
    }
    console.log('');

    // Summary
    console.log('📊 Test Summary:');
    if (statusData.enabled === false) {
      console.log('   - Booking is currently DISABLED');
      if (bookingResponse.status === 403 && paymentResponse.status === 403) {
        console.log('   - ✅ All security checks PASSED');
        console.log('   - ✅ Booking system is properly secured');
      } else {
        console.log('   - ❌ Security checks FAILED');
        console.log('   - ❌ Booking system is NOT properly secured');
      }
    } else {
      console.log('   - Booking is currently ENABLED');
      console.log('   - ⚠️  Cannot test security when booking is enabled');
      console.log('   - 💡 To test security, disable booking in admin panel first');
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.log('');
    console.log('💡 Troubleshooting:');
    console.log('   1. Make sure the development server is running');
    console.log('   2. Check that the BASE_URL is correct');
    console.log('   3. Verify database connection');
  }
}

// Run the test
if (require.main === module) {
  testBookingSecurity();
}

module.exports = { testBookingSecurity }; 