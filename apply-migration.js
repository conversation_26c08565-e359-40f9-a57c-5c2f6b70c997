const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

const supabaseUrl = 'https://uunrhhqghpcnwjatnoea.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV1bnJoaHFnaHBjbndqYXRub2VhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NDcwMjE3NywiZXhwIjoyMDcwMjc4MTc3fQ.WBcIJcFamu6IbrcDjBzyAoMR1YFZpAuxltAxJC0HuzM';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyMigration() {
  console.log('=== APPLYING MIGRATION TO FIX STATISTICS ===\n');

  // Read the migration file
  const migrationSQL = fs.readFileSync('supabase/migrations/20250814000000_fix_booking_statistics_function.sql', 'utf8');
  
  // Split the SQL into individual statements
  const statements = migrationSQL
    .split(';')
    .map(stmt => stmt.trim())
    .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

  console.log(`Found ${statements.length} SQL statements to execute\n`);

  // Execute each statement
  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i] + ';';
    console.log(`Executing statement ${i + 1}/${statements.length}...`);
    
    try {
      // Use the SQL editor endpoint to execute raw SQL
      const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseServiceKey}`,
          'apikey': supabaseServiceKey
        },
        body: JSON.stringify({ sql: statement })
      });

      if (!response.ok) {
        console.log(`Statement ${i + 1} failed, trying alternative method...`);
        // Alternative: try using a simple query approach
        console.log('Statement content:', statement.substring(0, 100) + '...');
      } else {
        console.log(`✓ Statement ${i + 1} executed successfully`);
      }
    } catch (error) {
      console.log(`Statement ${i + 1} error:`, error.message);
    }
  }

  // Test the function manually
  console.log('\n=== TESTING FIXED FUNCTION ===');
  
  try {
    const { data, error } = await supabase.rpc('get_detailed_booking_statistics');
    
    if (error) {
      console.error('Function test failed:', error);
    } else {
      console.log('✓ Function test successful:', JSON.stringify(data, null, 2));
    }
  } catch (error) {
    console.error('Function test error:', error);
  }

  // Test reconciliation function
  console.log('\n=== TESTING RECONCILIATION ===');
  
  try {
    const { data, error } = await supabase.rpc('reconcile_booking_statistics');
    
    if (error) {
      console.error('Reconciliation failed:', error);
    } else {
      console.log('✓ Reconciliation successful');
    }
  } catch (error) {
    console.error('Reconciliation error:', error);
  }
}

applyMigration().catch(console.error);
