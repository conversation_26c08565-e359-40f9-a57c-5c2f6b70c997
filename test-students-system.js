const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://uunrhhqghpcnwjatnoea.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV1bnJoaHFnaHBjbndqYXRub2VhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NDcwMjE3NywiZXhwIjoyMDcwMjc4MTc3fQ.WBcIJcFamu6IbrcDjBzyAoMR1YFZpAuxltAxJC0HuzM';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testStudentsSystem() {
  console.log('=== TESTING STUDENTS SYSTEM ===\n');

  // Test 1: Check if students table exists
  console.log('1. Checking if students table exists...');
  
  try {
    const { data, error } = await supabase
      .from('students')
      .select('*')
      .limit(1);
    
    if (error) {
      if (error.message.includes('relation "public.students" does not exist')) {
        console.log('❌ Students table does not exist');
        console.log('📝 You need to run the migration manually in Supabase SQL editor');
        console.log('📝 Copy the contents of: supabase/migrations/20250826000000_create_students_table.sql');
        return;
      } else {
        console.log('❌ Error checking students table:', error.message);
        return;
      }
    } else {
      console.log('✅ Students table exists');
      console.log(`   Found ${data.length} students in the table`);
    }
  } catch (error) {
    console.log('❌ Error testing students table:', error.message);
    return;
  }

  // Test 2: Test basic CRUD operations
  console.log('\n2. Testing CRUD operations...');
  
  try {
    // Create a test student
    const testStudent = {
      name: 'Test Student',
      admission_number: 'TEST123',
      hostel: 'Test Hostel',
      route: 'Test Route'
    };

    const { data: createdStudent, error: createError } = await supabase
      .from('students')
      .insert(testStudent)
      .select()
      .single();

    if (createError) {
      console.log('❌ Error creating test student:', createError.message);
      return;
    } else {
      console.log('✅ Successfully created test student');
    }

    // Read the student
    const { data: readStudent, error: readError } = await supabase
      .from('students')
      .select('*')
      .eq('id', createdStudent.id)
      .single();

    if (readError) {
      console.log('❌ Error reading test student:', readError.message);
    } else {
      console.log('✅ Successfully read test student');
    }

    // Update the student
    const { data: updatedStudent, error: updateError } = await supabase
      .from('students')
      .update({ name: 'Updated Test Student' })
      .eq('id', createdStudent.id)
      .select()
      .single();

    if (updateError) {
      console.log('❌ Error updating test student:', updateError.message);
    } else {
      console.log('✅ Successfully updated test student');
    }

    // Delete the student
    const { error: deleteError } = await supabase
      .from('students')
      .delete()
      .eq('id', createdStudent.id);

    if (deleteError) {
      console.log('❌ Error deleting test student:', deleteError.message);
    } else {
      console.log('✅ Successfully deleted test student');
    }

  } catch (error) {
    console.log('❌ Error during CRUD operations:', error.message);
  }

  // Test 3: Check sample data
  console.log('\n3. Checking sample data...');
  
  try {
    const { data: students, error } = await supabase
      .from('students')
      .select('*')
      .limit(10);
    
    if (error) {
      console.log('❌ Error fetching students:', error.message);
    } else {
      console.log(`✅ Found ${students.length} students in the database`);
      if (students.length > 0) {
        console.log('   Sample students:');
        students.forEach(student => {
          console.log(`   - ${student.name} (${student.admission_number})`);
        });
      }
    }
  } catch (error) {
    console.log('❌ Error checking sample data:', error.message);
  }

  console.log('\n=== STUDENTS SYSTEM TEST COMPLETE ===');
  console.log('\n📋 Next steps:');
  console.log('1. Open http://localhost:3000/admin in your browser');
  console.log('2. Login with username: admin, password: admin123');
  console.log('3. Click on "Manage Students" button');
  console.log('4. Test adding, editing, and deleting students');
  console.log('5. Test search and pagination functionality');
}

// Run the test
testStudentsSystem().catch(console.error);
