/*
  # Add Bus Name to Bookings Table
  
  This migration adds a bus_name column to the bookings table to store the bus name
  that corresponds to the route_code. This provides a direct reference to the bus
  name without requiring a join query.
*/

-- Add bus_name column to bookings table
ALTER TABLE bookings 
ADD COLUMN bus_name VARCHAR(100);

-- Add comment to explain the purpose of this column
COMMENT ON COLUMN bookings.bus_name IS 'The name of the bus corresponding to the route_code, retrieved from buses table';

-- Create index for better query performance on bus_name field
CREATE INDEX IF NOT EXISTS idx_bookings_bus_name ON bookings(bus_name);

-- Create composite index for bus-related queries
CREATE INDEX IF NOT EXISTS idx_bookings_route_bus_name ON bookings(bus_route, bus_name);

-- Update existing bookings with bus names from the buses table
UPDATE bookings 
SET bus_name = buses.name
FROM buses
WHERE bookings.bus_route = buses.route_code
  AND bookings.bus_name IS NULL; 