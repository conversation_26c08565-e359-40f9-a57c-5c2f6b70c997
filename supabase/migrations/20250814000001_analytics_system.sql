/*
  # Analytics System for Bus Pass Booking
  
  This migration creates a comprehensive analytics system with:
  1. analytics_revenue table for route-based revenue tracking
  2. booking_rounds table for historical booking data
  3. Triggers to automatically update analytics data
  4. Indexes for optimal query performance
  5. RLS policies for admin-only access
*/

-- Create analytics_revenue table
CREATE TABLE IF NOT EXISTS analytics_revenue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  bus_route TEXT NOT NULL REFERENCES buses(route_code) ON DELETE CASCADE,
  bus_name TEXT NOT NULL,
  total_revenue DECIMAL(10,2) NOT NULL DEFAULT 0.00 CHECK (total_revenue >= 0),
  booking_count INTEGER NOT NULL DEFAULT 0 CHECK (booking_count >= 0),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(bus_route)
);

-- Create booking_rounds table
CREATE TABLE IF NOT EXISTS booking_rounds (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  go_date DATE NOT NULL,
  return_date DATE NOT NULL,
  total_bookings INTEGER NOT NULL DEFAULT 0 CHECK (total_bookings >= 0),
  total_revenue DECIMAL(10,2) NOT NULL DEFAULT 0.00 CHECK (total_revenue >= 0),
  reset_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on new tables
ALTER TABLE analytics_revenue ENABLE ROW LEVEL SECURITY;
ALTER TABLE booking_rounds ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for admin-only access
CREATE POLICY "Admin users can view analytics_revenue"
  ON analytics_revenue
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Admin users can manage analytics_revenue"
  ON analytics_revenue
  FOR ALL
  TO authenticated
  USING (true);

CREATE POLICY "Admin users can view booking_rounds"
  ON booking_rounds
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Admin users can manage booking_rounds"
  ON booking_rounds
  FOR ALL
  TO authenticated
  USING (true);

-- Create indexes for optimal query performance
CREATE INDEX IF NOT EXISTS idx_analytics_revenue_bus_route ON analytics_revenue(bus_route);
CREATE INDEX IF NOT EXISTS idx_analytics_revenue_created_at ON analytics_revenue(created_at);
CREATE INDEX IF NOT EXISTS idx_analytics_revenue_total_revenue ON analytics_revenue(total_revenue DESC);
CREATE INDEX IF NOT EXISTS idx_booking_rounds_reset_date ON booking_rounds(reset_date DESC);
CREATE INDEX IF NOT EXISTS idx_booking_rounds_go_date ON booking_rounds(go_date);
CREATE INDEX IF NOT EXISTS idx_booking_rounds_total_revenue ON booking_rounds(total_revenue DESC);

-- Create function to update analytics_revenue when bookings change
CREATE OR REPLACE FUNCTION update_analytics_revenue()
RETURNS TRIGGER AS $$
DECLARE
  route_record RECORD;
  bus_name_val TEXT;
BEGIN
  -- Get bus name for the route
  SELECT name INTO bus_name_val
  FROM buses 
  WHERE route_code = COALESCE(NEW.bus_route, OLD.bus_route);
  
  -- Handle INSERT and UPDATE operations
  IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
    -- Get current stats for this route
    SELECT 
      COALESCE(SUM(CASE WHEN payment_status = true THEN fare ELSE 0 END), 0) as total_revenue,
      COUNT(*) as booking_count
    INTO route_record
    FROM bookings 
    WHERE bus_route = NEW.bus_route;
    
    -- Upsert analytics_revenue record
    INSERT INTO analytics_revenue (bus_route, bus_name, total_revenue, booking_count, updated_at)
    VALUES (NEW.bus_route, COALESCE(bus_name_val, NEW.bus_route), route_record.total_revenue, route_record.booking_count, NOW())
    ON CONFLICT (bus_route) 
    DO UPDATE SET 
      total_revenue = route_record.total_revenue,
      booking_count = route_record.booking_count,
      updated_at = NOW();
  END IF;
  
  -- Handle DELETE operations
  IF TG_OP = 'DELETE' THEN
    -- Get current stats for this route after deletion
    SELECT 
      COALESCE(SUM(CASE WHEN payment_status = true THEN fare ELSE 0 END), 0) as total_revenue,
      COUNT(*) as booking_count
    INTO route_record
    FROM bookings 
    WHERE bus_route = OLD.bus_route;
    
    -- Update analytics_revenue record
    UPDATE analytics_revenue 
    SET 
      total_revenue = route_record.total_revenue,
      booking_count = route_record.booking_count,
      updated_at = NOW()
    WHERE bus_route = OLD.bus_route;
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to archive current booking round
CREATE OR REPLACE FUNCTION archive_booking_round()
RETURNS VOID AS $$
DECLARE
  current_settings RECORD;
  total_paid_bookings INTEGER;
  total_unpaid_bookings INTEGER;
  total_all_bookings INTEGER;
  total_current_revenue DECIMAL(10,2);
BEGIN
  -- Get current admin settings
  SELECT go_date, return_date, current_revenue
  INTO current_settings
  FROM admin_settings
  WHERE id = 1;

  -- Get current booking stats
  SELECT
    COALESCE(paid_bookings, 0),
    COALESCE(unpaid_bookings, 0),
    COALESCE(current_revenue, 0.00)
  INTO total_paid_bookings, total_unpaid_bookings, total_current_revenue
  FROM admin_settings
  WHERE id = 1;

  -- Calculate total bookings (paid + unpaid)
  total_all_bookings := total_paid_bookings + total_unpaid_bookings;

  -- Only archive if there are actual bookings or revenue
  IF total_all_bookings > 0 OR total_current_revenue > 0 THEN
    INSERT INTO booking_rounds (
      go_date,
      return_date,
      total_bookings,
      total_revenue,
      reset_date
    ) VALUES (
      COALESCE(current_settings.go_date, CURRENT_DATE),
      COALESCE(current_settings.return_date, CURRENT_DATE + INTERVAL '1 day'),
      total_all_bookings,
      total_current_revenue,
      NOW()
    );

    RAISE NOTICE 'Archived booking round with % total bookings (% paid, % unpaid) and % revenue',
                 total_all_bookings, total_paid_bookings, total_unpaid_bookings, total_current_revenue;
  ELSE
    RAISE NOTICE 'No bookings or revenue to archive, skipping archival';
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for analytics_revenue updates
DROP TRIGGER IF EXISTS update_analytics_revenue_trigger ON bookings;
CREATE TRIGGER update_analytics_revenue_trigger
  AFTER INSERT OR UPDATE OR DELETE ON bookings
  FOR EACH ROW
  EXECUTE FUNCTION update_analytics_revenue();

-- Update the existing reset_all_bookings function to archive data first
CREATE OR REPLACE FUNCTION reset_all_bookings()
RETURNS VOID AS $$
BEGIN
  -- Archive current booking round before resetting
  PERFORM archive_booking_round();

  -- Reset available seats for all active buses to their total seats
  UPDATE buses
  SET available_seats = total_seats,
      updated_at = NOW()
  WHERE is_active = true;

  -- Reset booking statistics in admin_settings
  UPDATE admin_settings
  SET current_bookings = 0,
      paid_bookings = 0,
      unpaid_bookings = 0,
      current_revenue = 0.00,
      updated_at = NOW()
  WHERE id = 1;

  -- Reset analytics_revenue table
  DELETE FROM analytics_revenue;

  -- Log the reset operation
  RAISE NOTICE 'All booking statistics, bus seats, revenue, and analytics have been reset successfully.';
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Error resetting bookings: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Initialize analytics_revenue table with current data
INSERT INTO analytics_revenue (bus_route, bus_name, total_revenue, booking_count, updated_at)
SELECT
  b.route_code,
  b.name,
  COALESCE(SUM(CASE WHEN bk.payment_status = true THEN bk.fare ELSE 0 END), 0) as total_revenue,
  COUNT(bk.id) as booking_count,
  NOW()
FROM buses b
LEFT JOIN bookings bk ON b.route_code = bk.bus_route
WHERE b.is_active = true
GROUP BY b.route_code, b.name
ON CONFLICT (bus_route) DO UPDATE SET
  total_revenue = EXCLUDED.total_revenue,
  booking_count = EXCLUDED.booking_count,
  updated_at = EXCLUDED.updated_at;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION update_analytics_revenue() TO authenticated, anon;
GRANT EXECUTE ON FUNCTION archive_booking_round() TO authenticated;
GRANT EXECUTE ON FUNCTION reset_all_bookings() TO authenticated;
GRANT SELECT ON analytics_revenue TO authenticated, anon;
GRANT SELECT ON booking_rounds TO authenticated, anon;

-- Create updated_at trigger for analytics_revenue
CREATE TRIGGER update_analytics_revenue_updated_at
  BEFORE UPDATE ON analytics_revenue
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
