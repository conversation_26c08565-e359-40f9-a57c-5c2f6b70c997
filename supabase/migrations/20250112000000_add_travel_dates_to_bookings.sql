/*
  # Add Travel Dates to Bookings Table
  
  This migration adds go_date and return_date columns to the bookings table
  to track which travel dates were active when each booking was created.
  This provides historical tracking even if admin changes travel dates later.
*/

-- Add go_date and return_date columns to bookings table
ALTER TABLE bookings 
ADD COLUMN go_date DATE,
ADD COLUMN return_date DATE;

-- Add comment to explain the purpose of these columns
COMMENT ON COLUMN bookings.go_date IS 'The go date that was active when this booking was created';
COMMENT ON COLUMN bookings.return_date IS 'The return date that was active when this booking was created';

-- Create indexes for better query performance on date fields
CREATE INDEX IF NOT EXISTS idx_bookings_go_date ON bookings(go_date);
CREATE INDEX IF NOT EXISTS idx_bookings_return_date ON bookings(return_date);
CREATE INDEX IF NOT EXISTS idx_bookings_travel_dates ON bookings(go_date, return_date); 