/*
  # Fix Booking Trigger Issue
  
  This migration fixes the issue where the booking insertion fails due to
  a missing calculate_monthly_report function that's called by a trigger.
  
  The issue occurs because the analytics migration created a trigger that
  references functions that don't exist yet.
*/

-- First, let's check if the trigger exists and remove it if it's causing issues
DO $$
BEGIN
  -- Drop the problematic trigger if it exists
  IF EXISTS (
    SELECT 1 FROM pg_trigger 
    WHERE tgname = 'update_reports_on_booking_change'
  ) THEN
    DROP TRIGGER IF EXISTS update_reports_on_booking_change ON bookings;
    RAISE NOTICE 'Dropped problematic trigger update_reports_on_booking_change';
  END IF;
END $$;

-- Create a simplified version of the calculate_monthly_report function
CREATE OR REPLACE FUNCTION calculate_monthly_report(target_month DATE DEFAULT DATE_TRUNC('month', CURRENT_DATE))
RETURNS VOID AS $$
BEGIN
  -- This is a simplified version that does nothing for now
  -- It prevents the trigger from failing
  RAISE NOTICE 'calculate_monthly_report called for month: %', target_month;
  RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a simplified version of the calculate_daily_report function
CREATE OR REPLACE FUNCTION calculate_daily_report(target_date DATE DEFAULT CURRENT_DATE)
RETURNS VOID AS $$
BEGIN
  -- This is a simplified version that does nothing for now
  -- It prevents the trigger from failing
  RAISE NOTICE 'calculate_daily_report called for date: %', target_date;
  RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a simplified version of the trigger function
CREATE OR REPLACE FUNCTION trigger_update_reports()
RETURNS TRIGGER AS $$
BEGIN
  -- This is a simplified version that does nothing for now
  -- It prevents the trigger from failing
  RAISE NOTICE 'trigger_update_reports called for booking: %', NEW.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger with the simplified function
CREATE TRIGGER update_reports_on_booking_change
  AFTER INSERT OR UPDATE OR DELETE ON bookings
  FOR EACH ROW
  EXECUTE FUNCTION trigger_update_reports();

-- Add a comment to explain this is a temporary fix
COMMENT ON FUNCTION calculate_monthly_report(DATE) IS 'Temporary simplified version to prevent booking failures';
COMMENT ON FUNCTION calculate_daily_report(DATE) IS 'Temporary simplified version to prevent booking failures';
COMMENT ON FUNCTION trigger_update_reports() IS 'Temporary simplified version to prevent booking failures'; 