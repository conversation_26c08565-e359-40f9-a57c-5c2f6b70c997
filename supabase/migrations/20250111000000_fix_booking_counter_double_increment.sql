/*
  # Fix Booking Counter Double Increment Issue
  
  This migration fixes the critical issue where current_bookings was being incremented
  twice for online ticket bookings due to duplicate database triggers.
  
  Problem:
  - Two triggers were incrementing current_bookings: increment_booking_counter_trigger and booking_statistics_trigger
  - Online bookings (payment_status = true) triggered both, causing +2 increment
  - Upfront bookings (payment_status = false) triggered only one, causing +1 increment
  
  Solution:
  - Remove the duplicate increment_booking_counter_trigger
  - Keep only the booking_statistics_trigger which handles all booking types consistently
  - Ensure both online and upfront bookings increment current_bookings by exactly 1
*/

-- Drop the problematic trigger that was causing double increment for online bookings
DROP TRIGGER IF EXISTS increment_booking_counter_trigger ON bookings;

-- Drop the function that was only used by the removed trigger
DROP FUNCTION IF EXISTS trigger_increment_booking_counter();

-- Drop the increment_booking_counter function that was only used by the removed trigger
DROP FUNCTION IF EXISTS increment_booking_counter();

-- Verify that only the correct trigger remains
-- The booking_statistics_trigger from booking_management_functions.sql should be the only one
-- This trigger correctly increments current_bookings by 1 for ALL bookings (both online and upfront)

-- Add a comment to document the fix
COMMENT ON TRIGGER booking_statistics_trigger ON bookings IS 
'Handles booking counter increment for all booking types. Ensures current_bookings is incremented by exactly 1 for both online and upfront bookings.';

-- Log the fix
DO $$
BEGIN
  RAISE NOTICE 'Fixed booking counter double increment issue. Removed duplicate trigger that was causing online bookings to be counted twice.';
END $$; 