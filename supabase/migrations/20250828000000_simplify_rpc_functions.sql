-- Migration: Simplify RPC Functions
-- Date: 2025-08-28
-- Description: Simplifies the three main RPC functions to work with current_bookings table
-- and removes dependency on admin_settings for booking counters

-- 1. Update get_detailed_booking_statistics function to simplified version
CREATE OR REPLACE FUNCTION get_detailed_booking_statistics()
RETURNS TABLE(
  total_buses BIGINT,
  total_bookings BIGINT,
  current_bookings BIGINT,
  current_revenue DECIMAL(10,2),
  available_seats BIGINT,
  total_capacity BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    (SELECT COUNT(*) FROM buses b WHERE b.is_active = true) as total_buses,
    (SELECT COUNT(*) FROM bookings bk) as total_bookings,
    (SELECT COUNT(*) FROM current_bookings cb) as current_bookings,
    (SELECT COALESCE(SUM(cb.fare), 0.00) 
     FROM current_bookings cb) as current_revenue,
    (SELECT COALESCE(SUM(b.available_seats), 0) FROM buses b WHERE b.is_active = true) as available_seats,
    (SELECT COALESCE(SUM(b.total_seats), 0) FROM buses b WHERE b.is_active = true) as total_capacity;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Update handle_new_booking function to simplified version
CREATE OR REPLACE FUNCTION handle_new_booking()
RETURNS TRIGGER AS $$
BEGIN
  -- Log the booking for debugging
  RAISE NOTICE 'New booking processed: Student %, Route %, Payment Status %, Fare %', 
    NEW.student_name, NEW.bus_route, NEW.payment_status, COALESCE(NEW.fare, 0.00);
  
  -- Decrement available_seats for the specific bus route
  UPDATE buses 
  SET available_seats = GREATEST(COALESCE(available_seats, 0) - 1, 0),
      updated_at = NOW()
  WHERE route_code = NEW.bus_route AND is_active = true;
  
  -- Log seat update
  RAISE NOTICE 'Updated available seats for route %', NEW.bus_route;
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't fail the booking insertion
    RAISE WARNING 'Error in handle_new_booking trigger: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Update reset_all_bookings function to simplified version
CREATE OR REPLACE FUNCTION reset_all_bookings()
RETURNS VOID AS $$
BEGIN
  -- Log the start of reset operation
  RAISE NOTICE 'Starting reset_all_bookings operation';
  
  -- Delete all records from current_bookings table
  -- Use WHERE TRUE to satisfy RLS requirements
  DELETE FROM current_bookings WHERE TRUE;
  RAISE NOTICE 'Deleted all records from current_bookings table';
  
  -- Reset available_seats = total_seats for all active buses
  UPDATE buses
  SET available_seats = total_seats,
      updated_at = NOW()
  WHERE is_active = true;
  
  RAISE NOTICE 'Reset available_seats to total_seats for all active buses';
  
  -- Log completion
  RAISE NOTICE 'Reset operation completed successfully';
  
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Error in reset_all_bookings: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the trigger to use the simplified function
DROP TRIGGER IF EXISTS booking_statistics_trigger ON bookings;
CREATE TRIGGER booking_statistics_trigger
  AFTER INSERT ON bookings
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_booking();

-- Add comment for documentation
COMMENT ON FUNCTION get_detailed_booking_statistics() IS 'Simplified function that returns basic booking statistics from current_bookings and buses tables';
COMMENT ON FUNCTION handle_new_booking() IS 'Simplified trigger function that only decrements available_seats for bus routes';
COMMENT ON FUNCTION reset_all_bookings() IS 'Simplified function that deletes current_bookings and resets bus seats to total capacity';
