-- Fix the ambiguous column reference in get_detailed_booking_statistics function
-- This migration fixes the column ambiguity issue and ensures proper statistics

-- Drop the existing function if it exists
DROP FUNCTION IF EXISTS get_detailed_booking_statistics();

-- Create the corrected function with proper table aliases
CREATE OR REPLACE FUNCTION get_detailed_booking_statistics()
RETURNS TABLE(
  total_buses BIGINT,
  total_bookings BIGINT,
  current_bookings INTEGER,
  paid_bookings INTEGER,
  unpaid_bookings INTEGER,
  available_seats BIGINT,
  total_capacity BIGINT,
  current_revenue NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    (SELECT COUNT(*) FROM buses b WHERE b.is_active = true) as total_buses,
    (SELECT COUNT(*) FROM bookings bk) as total_bookings,
    (SELECT COALESCE(a.current_bookings, 0) FROM admin_settings a WHERE a.id = 1) as current_bookings,
    (SELECT COALESCE(a.paid_bookings, 0) FROM admin_settings a WHERE a.id = 1) as paid_bookings,
    (SELECT COALESCE(a.unpaid_bookings, 0) FROM admin_settings a WHERE a.id = 1) as unpaid_bookings,
    (SELECT COALESCE(SUM(b.available_seats), 0) FROM buses b WHERE b.is_active = true) as available_seats,
    (SELECT COUNT(*) * 50 FROM buses b WHERE b.is_active = true) as total_capacity,
    (SELECT COALESCE(a.current_revenue, 0) FROM admin_settings a WHERE a.id = 1) as current_revenue;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Also fix the trigger function to ensure it works properly
CREATE OR REPLACE FUNCTION handle_new_booking()
RETURNS TRIGGER AS $$
BEGIN
  -- Increment current_bookings by 1
  UPDATE admin_settings 
  SET current_bookings = COALESCE(current_bookings, 0) + 1,
      updated_at = NOW()
  WHERE id = 1;
  
  -- Update paid/unpaid bookings based on payment status
  IF NEW.payment_status = true THEN
    UPDATE admin_settings 
    SET paid_bookings = COALESCE(paid_bookings, 0) + 1,
        current_revenue = COALESCE(current_revenue, 0) + COALESCE(NEW.fare, 0),
        updated_at = NOW()
    WHERE id = 1;
  ELSE
    UPDATE admin_settings 
    SET unpaid_bookings = COALESCE(unpaid_bookings, 0) + 1,
        updated_at = NOW()
    WHERE id = 1;
  END IF;
  
  -- Decrement available_seats for the specific bus route
  UPDATE buses 
  SET available_seats = GREATEST(COALESCE(available_seats, 0) - 1, 0),
      updated_at = NOW()
  WHERE route_code = NEW.bus_route AND is_active = true;
  
  -- Log the booking for debugging (optional)
  RAISE NOTICE 'New booking processed: Student %, Route %, Payment Status %', 
    NEW.student_name, NEW.bus_route, NEW.payment_status;
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't fail the booking insertion
    RAISE WARNING 'Error in handle_new_booking trigger: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger
DROP TRIGGER IF EXISTS booking_statistics_trigger ON bookings;
CREATE TRIGGER booking_statistics_trigger
  AFTER INSERT ON bookings
  FOR EACH ROW
  EXECUTE FUNCTION handle_new_booking();

-- Create a function to reconcile existing data
CREATE OR REPLACE FUNCTION reconcile_booking_statistics()
RETURNS VOID AS $$
DECLARE
  total_count INTEGER;
  paid_count INTEGER;
  unpaid_count INTEGER;
  total_revenue NUMERIC;
BEGIN
  -- Count actual bookings
  SELECT COUNT(*) INTO total_count FROM bookings;
  SELECT COUNT(*) INTO paid_count FROM bookings WHERE payment_status = true;
  SELECT COUNT(*) INTO unpaid_count FROM bookings WHERE payment_status = false;
  SELECT COALESCE(SUM(fare), 0) INTO total_revenue FROM bookings WHERE payment_status = true;
  
  -- Update admin_settings with correct values
  UPDATE admin_settings 
  SET current_bookings = total_count,
      paid_bookings = paid_count,
      unpaid_bookings = unpaid_count,
      current_revenue = total_revenue,
      updated_at = NOW()
  WHERE id = 1;
  
  RAISE NOTICE 'Statistics reconciled: Total=%, Paid=%, Unpaid=%, Revenue=%', 
    total_count, paid_count, unpaid_count, total_revenue;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
