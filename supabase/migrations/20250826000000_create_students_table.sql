/*
  # Students Table Migration
  
  This migration creates the students table for the Student Management System:
  - students: Student information with admission numbers, hostel wardens, and routes
  
  Features:
  - UUID primary key for better scalability
  - Unique admission number constraint
  - Proper indexing for performance
  - Row Level Security (RLS) policies
  - Audit timestamps
*/

-- Create students table
CREATE TABLE IF NOT EXISTS students (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  admission_number VARCHAR(7) NOT NULL UNIQUE,
  hostel VARCHAR(255),
  route VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on students table
ALTER TABLE students ENABLE ROW LEVEL SECURITY;

-- Create policies for students table (admin access only)
CREATE POLICY "Admin users can view students"
  ON students
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Admin users can insert students"
  ON students
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Admin users can update students"
  ON students
  FOR UPDATE
  TO authenticated
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Admin users can delete students"
  ON students
  FOR DELETE
  TO authenticated
  USING (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_students_admission_number ON students(admission_number);
CREATE INDEX IF NOT EXISTS idx_students_name ON students(name);
CREATE INDEX IF NOT EXISTS idx_students_hostel ON students(hostel);
CREATE INDEX IF NOT EXISTS idx_students_route ON students(route);
CREATE INDEX IF NOT EXISTS idx_students_created_at ON students(created_at);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_students_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER update_students_updated_at
  BEFORE UPDATE ON students
  FOR EACH ROW
  EXECUTE FUNCTION update_students_updated_at();


