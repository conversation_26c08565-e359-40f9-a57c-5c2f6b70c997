/*
  # Current Bookings Table Migration
  
  This migration implements a new booking tracking system:
  1. Creates current_bookings table with same schema as bookings table
  2. Migrates existing booking counts from admin_settings to actual booking records
  3. Drops paid_bookings and unpaid_bookings columns from admin_settings
  
  Features:
  - Atomic migration with proper error handling
  - Data preservation during migration
  - Proper indexing for performance
  - Row Level Security (RLS) policies
*/

-- Create current_bookings table with same schema as bookings
CREATE TABLE IF NOT EXISTS current_bookings (
  id SERIAL PRIMARY KEY,
  admission_number VARCHAR(7) NOT NULL,
  student_name VARCHAR(100) NOT NULL,
  bus_route VARCHAR(50) NOT NULL,
  destination VARCHAR(100) NOT NULL,
  payment_status BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  go_date DATE,
  return_date DATE,
  fare DECIMAL(10,2) CHECK (fare >= 0),
  bus_name VARCHAR(100),
  razorpay_payment_id VARCHAR(255),
  razorpay_order_id VARCHAR(255),
  razorpay_signature VARCHAR(512)
);

-- <PERSON>reate indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_current_bookings_admission_number ON current_bookings(admission_number);
CREATE INDEX IF NOT EXISTS idx_current_bookings_bus_route ON current_bookings(bus_route);
CREATE INDEX IF NOT EXISTS idx_current_bookings_payment_status ON current_bookings(payment_status);
CREATE INDEX IF NOT EXISTS idx_current_bookings_created_at ON current_bookings(created_at);
CREATE INDEX IF NOT EXISTS idx_current_bookings_go_date ON current_bookings(go_date);
CREATE INDEX IF NOT EXISTS idx_current_bookings_return_date ON current_bookings(return_date);
CREATE INDEX IF NOT EXISTS idx_current_bookings_travel_dates ON current_bookings(go_date, return_date);
CREATE INDEX IF NOT EXISTS idx_current_bookings_route_destination_fare ON current_bookings(bus_route, destination, fare);

-- Enable RLS on current_bookings table
ALTER TABLE current_bookings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for current_bookings (same as bookings table)
CREATE POLICY "Allow public current_bookings insert"
  ON current_bookings
  FOR INSERT
  TO anon, authenticated
  WITH CHECK (true);

CREATE POLICY "Allow authenticated current_bookings select"
  ON current_bookings
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated current_bookings update"
  ON current_bookings
  FOR UPDATE
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated current_bookings delete"
  ON current_bookings
  FOR DELETE
  TO authenticated
  USING (true);

-- Add comment to explain the purpose of this table
COMMENT ON TABLE current_bookings IS 'Tracks current/active bookings for the upcoming trip. This table is truncated during reset operations instead of using counters in admin_settings.';

-- Migration function to convert existing booking counts to actual records
CREATE OR REPLACE FUNCTION migrate_booking_counts_to_current_bookings()
RETURNS VOID AS $$
DECLARE
  paid_count INTEGER := 0;
  unpaid_count INTEGER := 0;
  current_go_date DATE;
  current_return_date DATE;
  i INTEGER;
BEGIN
  -- Get current booking counts and travel dates from admin_settings
  SELECT 
    COALESCE(paid_bookings, 0),
    COALESCE(unpaid_bookings, 0),
    go_date,
    return_date
  INTO paid_count, unpaid_count, current_go_date, current_return_date
  FROM admin_settings 
  WHERE id = 1;

  -- Log the migration details
  RAISE NOTICE 'Starting migration: % paid bookings, % unpaid bookings', paid_count, unpaid_count;
  RAISE NOTICE 'Travel dates: go_date=%, return_date=%', current_go_date, current_return_date;

  -- Create placeholder records for paid bookings
  FOR i IN 1..paid_count LOOP
    INSERT INTO current_bookings (
      admission_number,
      student_name,
      bus_route,
      destination,
      payment_status,
      created_at,
      go_date,
      return_date,
      fare,
      bus_name
    ) VALUES (
      'MIG' || LPAD(i::text, 4, '0'), -- MIG0001, MIG0002, etc.
      'Migrated Booking ' || i,
      'bus-1', -- Default bus route
      'Default Destination',
      true, -- Paid booking
      NOW() - INTERVAL '1 hour' * i, -- Stagger creation times
      current_go_date,
      current_return_date,
      100.00, -- Default fare
      'Bus 1' -- Default bus name
    );
  END LOOP;

  -- Create placeholder records for unpaid bookings
  FOR i IN 1..unpaid_count LOOP
    INSERT INTO current_bookings (
      admission_number,
      student_name,
      bus_route,
      destination,
      payment_status,
      created_at,
      go_date,
      return_date,
      fare,
      bus_name
    ) VALUES (
      'UMG' || LPAD(i::text, 4, '0'), -- UMG0001, UMG0002, etc.
      'Migrated Unpaid Booking ' || i,
      'bus-1', -- Default bus route
      'Default Destination',
      false, -- Unpaid booking
      NOW() - INTERVAL '1 hour' * (paid_count + i), -- Stagger creation times
      current_go_date,
      current_return_date,
      100.00, -- Default fare
      'Bus 1' -- Default bus name
    );
  END LOOP;

  RAISE NOTICE 'Migration completed: Created % total booking records in current_bookings', paid_count + unpaid_count;
EXCEPTION
  WHEN OTHERS THEN
    RAISE EXCEPTION 'Error during booking counts migration: %', SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Execute the migration
SELECT migrate_booking_counts_to_current_bookings();

-- Drop the migration function as it's no longer needed
DROP FUNCTION migrate_booking_counts_to_current_bookings();

-- Drop the paid_bookings and unpaid_bookings columns from admin_settings
-- First check if columns exist before dropping them
DO $$
BEGIN
  -- Drop paid_bookings column if it exists
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'admin_settings' 
    AND column_name = 'paid_bookings'
  ) THEN
    ALTER TABLE admin_settings DROP COLUMN paid_bookings;
    RAISE NOTICE 'Dropped paid_bookings column from admin_settings';
  ELSE
    RAISE NOTICE 'paid_bookings column does not exist in admin_settings';
  END IF;

  -- Drop unpaid_bookings column if it exists
  IF EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'admin_settings' 
    AND column_name = 'unpaid_bookings'
  ) THEN
    ALTER TABLE admin_settings DROP COLUMN unpaid_bookings;
    RAISE NOTICE 'Dropped unpaid_bookings column from admin_settings';
  ELSE
    RAISE NOTICE 'unpaid_bookings column does not exist in admin_settings';
  END IF;
END;
$$;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON current_bookings TO authenticated;
GRANT USAGE, SELECT ON SEQUENCE current_bookings_id_seq TO authenticated;

-- Log completion
DO $$
BEGIN
  RAISE NOTICE 'Current bookings table migration completed successfully';
  RAISE NOTICE 'Table: current_bookings created with proper schema and indexes';
  RAISE NOTICE 'RLS policies: Applied for secure access';
  RAISE NOTICE 'Migration: Existing booking counts converted to actual records';
  RAISE NOTICE 'Cleanup: Removed paid_bookings and unpaid_bookings columns from admin_settings';
END;
$$;
