/*
  # Migration: Bus-x to Route-x Identifier Refactoring
  
  This migration safely changes all bus identifiers from "bus-x" pattern to "route-x" pattern
  across the entire database while maintaining referential integrity and data consistency.
  
  AFFECTED TABLES:
  - buses (primary table with route_code)
  - route_stops (foreign key to buses.route_code)
  - bookings (bus_route column)
  - analytics_revenue (bus_route column with foreign key)
  - students (route column)
  - bus_availability (if exists - legacy table)
  
  SAFETY MEASURES:
  - Single transaction for atomicity
  - Comprehensive validation checks
  - Detailed logging for audit trail
  - Rollback procedures included
*/

-- Start transaction for atomicity
BEGIN;

-- Create a backup table for rollback purposes
CREATE TEMP TABLE migration_backup AS
SELECT 
  'buses' as table_name,
  route_code as old_value,
  REPLACE(route_code, 'bus-', 'route-') as new_value
FROM buses 
WHERE route_code LIKE 'bus-%';

-- Log migration start
DO $$
BEGIN
  RAISE NOTICE 'Starting bus-x to route-x migration at %', NOW();
  RAISE NOTICE 'Found % bus routes to migrate', (SELECT COUNT(*) FROM buses WHERE route_code LIKE 'bus-%');
END $$;

-- STEP 1: Temporarily disable foreign key constraints to allow safe updates
-- We'll re-enable them after the migration
ALTER TABLE route_stops DROP CONSTRAINT IF EXISTS route_stops_route_code_fkey;
ALTER TABLE analytics_revenue DROP CONSTRAINT IF EXISTS analytics_revenue_bus_route_fkey;

-- STEP 2: Update the primary buses table
-- This is the source of truth for route codes
UPDATE buses 
SET route_code = REPLACE(route_code, 'bus-', 'route-'),
    updated_at = NOW()
WHERE route_code LIKE 'bus-%';

-- Validate buses table update
DO $$
DECLARE
  bus_count INTEGER;
  route_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO bus_count FROM buses WHERE route_code LIKE 'bus-%';
  SELECT COUNT(*) INTO route_count FROM buses WHERE route_code LIKE 'route-%';
  
  IF bus_count > 0 THEN
    RAISE EXCEPTION 'Migration failed: Still found % bus-x identifiers in buses table', bus_count;
  END IF;
  
  RAISE NOTICE 'Buses table updated successfully: % routes now use route-x pattern', route_count;
END $$;

-- STEP 3: Update route_stops table
UPDATE route_stops 
SET route_code = REPLACE(route_code, 'bus-', 'route-'),
    updated_at = NOW()
WHERE route_code LIKE 'bus-%';

-- Validate route_stops table update
DO $$
DECLARE
  bus_count INTEGER;
  route_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO bus_count FROM route_stops WHERE route_code LIKE 'bus-%';
  SELECT COUNT(*) INTO route_count FROM route_stops WHERE route_code LIKE 'route-%';
  
  IF bus_count > 0 THEN
    RAISE EXCEPTION 'Migration failed: Still found % bus-x identifiers in route_stops table', bus_count;
  END IF;
  
  RAISE NOTICE 'Route_stops table updated successfully: % stops now reference route-x pattern', route_count;
END $$;

-- STEP 4: Update bookings table
UPDATE bookings 
SET bus_route = REPLACE(bus_route, 'bus-', 'route-')
WHERE bus_route LIKE 'bus-%';

-- Validate bookings table update
DO $$
DECLARE
  bus_count INTEGER;
  route_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO bus_count FROM bookings WHERE bus_route LIKE 'bus-%';
  SELECT COUNT(*) INTO route_count FROM bookings WHERE bus_route LIKE 'route-%';
  
  IF bus_count > 0 THEN
    RAISE EXCEPTION 'Migration failed: Still found % bus-x identifiers in bookings table', bus_count;
  END IF;
  
  RAISE NOTICE 'Bookings table updated successfully: % bookings now reference route-x pattern', route_count;
END $$;

-- STEP 5: Update analytics_revenue table
UPDATE analytics_revenue 
SET bus_route = REPLACE(bus_route, 'bus-', 'route-'),
    updated_at = NOW()
WHERE bus_route LIKE 'bus-%';

-- Validate analytics_revenue table update
DO $$
DECLARE
  bus_count INTEGER;
  route_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO bus_count FROM analytics_revenue WHERE bus_route LIKE 'bus-%';
  SELECT COUNT(*) INTO route_count FROM analytics_revenue WHERE bus_route LIKE 'route-%';
  
  IF bus_count > 0 THEN
    RAISE EXCEPTION 'Migration failed: Still found % bus-x identifiers in analytics_revenue table', bus_count;
  END IF;
  
  RAISE NOTICE 'Analytics_revenue table updated successfully: % records now reference route-x pattern', route_count;
END $$;

-- STEP 6: Update students table (route column may contain bus route references)
UPDATE students 
SET route = REPLACE(route, 'bus-', 'route-'),
    updated_at = NOW()
WHERE route LIKE 'bus-%';

-- Validate students table update
DO $$
DECLARE
  bus_count INTEGER;
  route_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO bus_count FROM students WHERE route LIKE 'bus-%';
  SELECT COUNT(*) INTO route_count FROM students WHERE route LIKE 'route-%';
  
  RAISE NOTICE 'Students table updated: % students now reference route-x pattern', route_count;
  IF bus_count > 0 THEN
    RAISE NOTICE 'Warning: % students still have bus-x references (may be intentional)', bus_count;
  END IF;
END $$;

-- STEP 7: Update bus_availability table if it exists (legacy table)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'bus_availability') THEN
    UPDATE bus_availability 
    SET bus_route = REPLACE(bus_route, 'bus-', 'route-'),
        updated_at = NOW()
    WHERE bus_route LIKE 'bus-%';
    
    RAISE NOTICE 'Legacy bus_availability table updated successfully';
  ELSE
    RAISE NOTICE 'Legacy bus_availability table not found (expected if using consolidated schema)';
  END IF;
END $$;

-- STEP 8: Re-enable foreign key constraints with updated references
ALTER TABLE route_stops 
ADD CONSTRAINT route_stops_route_code_fkey 
FOREIGN KEY (route_code) REFERENCES buses(route_code) ON DELETE CASCADE;

ALTER TABLE analytics_revenue 
ADD CONSTRAINT analytics_revenue_bus_route_fkey 
FOREIGN KEY (bus_route) REFERENCES buses(route_code) ON DELETE CASCADE;

-- STEP 9: Validate all foreign key relationships
DO $$
DECLARE
  orphaned_stops INTEGER;
  orphaned_analytics INTEGER;
BEGIN
  -- Check for orphaned route_stops
  SELECT COUNT(*) INTO orphaned_stops
  FROM route_stops rs
  LEFT JOIN buses b ON rs.route_code = b.route_code
  WHERE b.route_code IS NULL;
  
  -- Check for orphaned analytics_revenue
  SELECT COUNT(*) INTO orphaned_analytics
  FROM analytics_revenue ar
  LEFT JOIN buses b ON ar.bus_route = b.route_code
  WHERE b.route_code IS NULL;
  
  IF orphaned_stops > 0 THEN
    RAISE EXCEPTION 'Migration validation failed: Found % orphaned route_stops records', orphaned_stops;
  END IF;
  
  IF orphaned_analytics > 0 THEN
    RAISE EXCEPTION 'Migration validation failed: Found % orphaned analytics_revenue records', orphaned_analytics;
  END IF;
  
  RAISE NOTICE 'Foreign key validation passed: All relationships are intact';
END $$;

-- STEP 10: Final validation and summary
DO $$
DECLARE
  total_buses INTEGER;
  total_stops INTEGER;
  total_bookings INTEGER;
  total_analytics INTEGER;
BEGIN
  SELECT COUNT(*) INTO total_buses FROM buses WHERE route_code LIKE 'route-%';
  SELECT COUNT(*) INTO total_stops FROM route_stops WHERE route_code LIKE 'route-%';
  SELECT COUNT(*) INTO total_bookings FROM bookings WHERE bus_route LIKE 'route-%';
  SELECT COUNT(*) INTO total_analytics FROM analytics_revenue WHERE bus_route LIKE 'route-%';
  
  RAISE NOTICE '=== MIGRATION COMPLETED SUCCESSFULLY ===';
  RAISE NOTICE 'Buses migrated: %', total_buses;
  RAISE NOTICE 'Route stops migrated: %', total_stops;
  RAISE NOTICE 'Bookings migrated: %', total_bookings;
  RAISE NOTICE 'Analytics records migrated: %', total_analytics;
  RAISE NOTICE 'Migration completed at: %', NOW();
END $$;

-- Commit the transaction
COMMIT;

-- Create rollback script as a comment for reference
/*
ROLLBACK SCRIPT (Run only if migration needs to be reversed):

BEGIN;

-- Disable foreign key constraints
ALTER TABLE route_stops DROP CONSTRAINT IF EXISTS route_stops_route_code_fkey;
ALTER TABLE analytics_revenue DROP CONSTRAINT IF EXISTS analytics_revenue_bus_route_fkey;

-- Reverse the migration
UPDATE buses SET route_code = REPLACE(route_code, 'route-', 'bus-'), updated_at = NOW() WHERE route_code LIKE 'route-%';
UPDATE route_stops SET route_code = REPLACE(route_code, 'route-', 'bus-'), updated_at = NOW() WHERE route_code LIKE 'route-%';
UPDATE bookings SET bus_route = REPLACE(bus_route, 'route-', 'bus-') WHERE bus_route LIKE 'route-%';
UPDATE analytics_revenue SET bus_route = REPLACE(bus_route, 'route-', 'bus-'), updated_at = NOW() WHERE bus_route LIKE 'route-%';
UPDATE students SET route = REPLACE(route, 'route-', 'bus-'), updated_at = NOW() WHERE route LIKE 'route-%';

-- Re-enable foreign key constraints
ALTER TABLE route_stops ADD CONSTRAINT route_stops_route_code_fkey FOREIGN KEY (route_code) REFERENCES buses(route_code) ON DELETE CASCADE;
ALTER TABLE analytics_revenue ADD CONSTRAINT analytics_revenue_bus_route_fkey FOREIGN KEY (bus_route) REFERENCES buses(route_code) ON DELETE CASCADE;

COMMIT;
*/
