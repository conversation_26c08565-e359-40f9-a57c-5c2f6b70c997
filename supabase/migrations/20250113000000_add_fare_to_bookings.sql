/*
  # Add Fare Column to Bookings Table
  
  This migration adds a fare column to the bookings table to store the fare amount
  for the selected destination. The fare value is retrieved from the route_stops table
  based on the booking's route and destination combination.
*/

-- Add fare column to bookings table
ALTER TABLE bookings 
ADD COLUMN fare DECIMAL(10,2) CHECK (fare >= 0);

-- Add comment to explain the purpose of this column
COMMENT ON COLUMN bookings.fare IS 'The fare amount for the selected destination, retrieved from route_stops table';

-- Create index for better query performance on fare field
CREATE INDEX IF NOT EXISTS idx_bookings_fare ON bookings(fare);

-- Create composite index for fare-related queries
CREATE INDEX IF NOT EXISTS idx_bookings_route_destination_fare ON bookings(bus_route, destination, fare); 