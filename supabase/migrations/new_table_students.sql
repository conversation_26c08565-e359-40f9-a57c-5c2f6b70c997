-- Create students table
CREATE TABLE IF NOT EXISTS students (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VA<PERSON>HAR(255) NOT NULL,
  admission_number VARCHAR(7) NOT NULL UNIQUE,
  hostel VARCHAR(255),
  route VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_students_admission_number ON students(admission_number);
CREATE INDEX IF NOT EXISTS idx_students_route ON students(route);

-- Add RLS (Row Level Security) policies if needed
ALTER TABLE students ENABLE ROW LEVEL SECURITY;

-- Example RLS policy (adjust based on your authentication requirements)
CREATE POLICY "Allow public read access to students" 
ON students FOR SELECT 
TO public 
USING (true);

-- Add trigger for updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_students_updated_at 
    BEFORE UPDATE ON students 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();