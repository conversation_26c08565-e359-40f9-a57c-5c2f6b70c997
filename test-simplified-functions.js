// Test script for simplified RPC functions
// This script tests the updated API endpoints to ensure they work with the simplified functions

const BASE_URL = 'http://localhost:3001';

async function testDetailedStats() {
  console.log('\n=== Testing Detailed Statistics API ===');
  
  try {
    const response = await fetch(`${BASE_URL}/api/admin/analytics/detailed-stats`);
    const data = await response.json();
    
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      console.log('✅ Detailed stats API working correctly');
      
      // Validate expected fields are present
      const expectedFields = ['totalBuses', 'totalBookings', 'currentBookings', 'currentRevenue', 'availableSeats', 'totalCapacity', 'occupancyRate'];
      const missingFields = expectedFields.filter(field => !(field in data.data));
      
      if (missingFields.length === 0) {
        console.log('✅ All expected fields are present');
      } else {
        console.log('❌ Missing fields:', missingFields);
      }
      
      // Validate removed fields are not present
      const removedFields = ['paidBookings', 'unpaidBookings'];
      const unexpectedFields = removedFields.filter(field => field in data.data);
      
      if (unexpectedFields.length === 0) {
        console.log('✅ Removed fields are not present');
      } else {
        console.log('❌ Unexpected fields found:', unexpectedFields);
      }
    } else {
      console.log('❌ API returned error:', data.error);
    }
  } catch (error) {
    console.log('❌ Error testing detailed stats:', error.message);
  }
}

async function testResetFunction() {
  console.log('\n=== Testing Reset Function API ===');
  
  try {
    // Note: This would require authentication in a real scenario
    // For testing purposes, we'll just check if the endpoint exists
    const response = await fetch(`${BASE_URL}/api/admin/analytics/reset`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Response status:', response.status);
    
    if (response.status === 401 || response.status === 403) {
      console.log('✅ Reset endpoint exists and requires authentication (expected)');
    } else {
      const data = await response.json();
      console.log('Response data:', JSON.stringify(data, null, 2));
      
      if (data.success) {
        console.log('✅ Reset function executed successfully');
      } else {
        console.log('⚠️ Reset function returned error (may be expected without auth):', data.error);
      }
    }
  } catch (error) {
    console.log('❌ Error testing reset function:', error.message);
  }
}

async function runTests() {
  console.log('🧪 Testing Simplified RPC Functions');
  console.log('=====================================');
  
  await testDetailedStats();
  await testResetFunction();
  
  console.log('\n✅ Test suite completed');
}

// Run the tests
runTests().catch(console.error);
